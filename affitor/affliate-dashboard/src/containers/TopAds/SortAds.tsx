import React from 'react';
import { ArrowUpDown } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SortOption {
  value: string;
  label: string;
  sortField: string;
  order: 'desc' | 'asc';
}

interface SortAdsProps {
  onSortChange: (sortField: string, order: 'desc' | 'asc') => void;
  currentSort: { field: string; order: 'desc' | 'asc' };
}

const SortAds: React.FC<SortAdsProps> = ({ onSortChange, currentSort }) => {
  const options: SortOption[] = [
    { value: 'ctr-desc', label: 'Top CTR', sortField: 'ctr', order: 'desc' },
    // { value: 'clicks-desc', label: 'Top Clicks', sortField: 'clicks', order: 'desc' },
    { value: 'views-desc', label: 'Top Views', sortField: 'views', order: 'desc' },
    { value: 'likes-desc', label: 'Top Likes', sortField: 'likes', order: 'desc' },
    { value: 'comments-desc', label: 'Top Comments', sortField: 'comments', order: 'desc' },
    { value: 'shares-desc', label: 'Top Shares', sortField: 'shares', order: 'desc' },
    // { value: 'avgCostPerDay-desc', label: 'Highest Cost/Day', sortField: 'avgCostPerDay', order: 'desc' },
    // { value: 'avgCostPerDay-asc', label: 'Lowest Cost/Day', sortField: 'avgCostPerDay', order: 'asc' },
  ];

  // Determine current sort value
  const currentSortValue = `${currentSort.field}-${currentSort.order}`;
  
  // Handle selection change
  const handleSortChange = (value: string) => {
    const selectedOption = options.find(option => option.value === value);
    if (selectedOption) {
      onSortChange(selectedOption.sortField, selectedOption.order);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <ArrowUpDown size={16} className="text-gray-400" />
      <Select value={currentSortValue} onValueChange={handleSortChange}>
        <SelectTrigger className="h-9 w-[170px]">
          <SelectValue placeholder="Sort by..." />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default SortAds;
