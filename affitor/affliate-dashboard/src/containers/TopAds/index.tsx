import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { actions } from "@/features/top-ads/top-ads.slice";
import { actions as userActions } from "@/features/user/user.slice";
import {
  selectTopAdsList,
  selectTopAdsLoading,
  selectTopAdsPagination,
  selectUserIsPremium,
  selectUserLoading,
  selectUserData,
  selectIsAuthenticated,
} from "@/features/selectors";
import { CircularProgress } from "@mui/material";
import TableAds from "./TableAds";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarIcon, SearchIcon, LockIcon } from "lucide-react";
import SortAds from "./SortAds"; // Changed from SortVideos to SortAds
import FilterPanel, { VideoFilterState } from "../TopVideos/FilterPanel";
import { Input } from "@/components/ui/input";
import { CustomButton } from "@/components/CustomButton";
import { Loading } from "@/components";
import { Button } from "@/components/ui/button";
import Link from "next/link";

// Helper function to parse URL hash to get filters
const parseHashParams = (hash: string) => {
  if (!hash || hash === "#")
    return {
      timePeriod: "30days",
      sort: { field: "views", order: "desc" as const },
      filters: {
        platforms: [],
        categories: [],
        minViews: "",
      },
      searchTerm: "",
    };

  // Remove the leading # and split by #
  const hashParts = hash.substring(1).split("#");
  const params: {
    timePeriod: string;
    sort: { field: string; order: "desc" | "asc" };
    filters: VideoFilterState;
    searchTerm: string;
  } = {
    timePeriod: "30days",
    sort: { field: "views", order: "desc" as const },
    filters: {
      platforms: [],
      categories: [],
      minViews: "",
    },
    searchTerm: "",
  };

  for (const part of hashParts) {
    const equalSignIndex = part.indexOf("=");
    if (equalSignIndex === -1) continue;

    const key = part.substring(0, equalSignIndex);
    const value = decodeURIComponent(part.substring(equalSignIndex + 1));

    if (key === "period") {
      params.timePeriod = value;
    } else if (key === "sort") {
      const [field, order] = value.split("-");
      params.sort = { field, order: order as "desc" | "asc" };
    } else if (key === "platforms") {
      params.filters.platforms = value ? value.split(",") : [];
    } else if (key === "categories") {
      params.filters.categories = value ? value.split(",") : [];
    } else if (key === "minViews") {
      params.filters.minViews = value;
    } else if (key === "search") {
      params.searchTerm = value;
    }
  }

  return params;
};

// Helper function to convert filters to hash
const filtersToHash = (
  timePeriod: string,
  sort: { field: string; order: "desc" | "asc" },
  filters: VideoFilterState,
  searchTerm: string
): string => {
  // Get existing hash to preserve tab
  const currentHash = typeof window !== "undefined" ? window.location.hash : "";
  const hashParts = currentHash ? currentHash.substring(1).split("#") : [];
  const newParts: string[] = [];

  // Preserve tab parameter if it exists
  const tabPart = hashParts.find((part) => part.startsWith("tab="));
  if (tabPart) {
    newParts.push(tabPart);
  } else {
    newParts.push("tab=ads"); // Default tab for ads
  }

  newParts.push(`period=${encodeURIComponent(timePeriod)}`);
  newParts.push(`sort=${encodeURIComponent(`${sort.field}-${sort.order}`)}`);

  // Add filters
  if (filters.platforms.length > 0) {
    newParts.push(
      `platforms=${encodeURIComponent(filters.platforms.join(","))}`
    );
  }

  if (filters.categories.length > 0) {
    newParts.push(
      `categories=${encodeURIComponent(filters.categories.join(","))}`
    );
  }

  if (filters.minViews) {
    newParts.push(`minViews=${encodeURIComponent(filters.minViews)}`);
  }

  // Add search term
  if (searchTerm) {
    newParts.push(`search=${encodeURIComponent(searchTerm)}`);
  }

  return `#${newParts.join("#")}`;
};

// Helper function to update URL hash without refresh
const updateUrlHash = (hash: string) => {
  if (typeof window === "undefined") return;

  // Use replaceState to update URL without navigation
  window.history.replaceState(
    null,
    "",
    hash || window.location.pathname + window.location.search
  );
};

const TopAds: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const topAds = useSelector(selectTopAdsList);
  const topAdsPagination = useSelector(selectTopAdsPagination);
  const loading = useSelector(selectTopAdsLoading);
  const isPremium = useSelector(selectUserIsPremium);
  const userLoading = useSelector(selectUserLoading);
  const userData = useSelector(selectUserData);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  const [currentPage, setCurrentPage] = useState(1);
  const [isHashInitialized, setIsHashInitialized] = useState(false);

  // State for filters and sort - initialize with CTR as default sort
  const [timePeriod, setTimePeriod] = useState("30days");
  const [sortOption, setSortOption] = useState<{
    field: string;
    order: "desc" | "asc";
  }>({
    field: "ctr",
    order: "desc",
  });
  const [filterOptions, setFilterOptions] = useState<VideoFilterState>({
    platforms: [],
    categories: [],
    minViews: "",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  // Debounce search term changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Process URL hash on initial load
  useEffect(() => {
    if (typeof window !== "undefined" && !isHashInitialized) {
      // Get hash and parse params
      const hash = window.location.hash;

      if (hash) {
        const params = parseHashParams(hash);
        setTimePeriod(params.timePeriod);
        setSortOption(params.sort);
        setFilterOptions(params.filters);
        setSearchTerm(params.searchTerm);
        setDebouncedSearchTerm(params.searchTerm);

        // Update Redux store with these values
        dispatch(actions.setTimePeriod(params.timePeriod));
        dispatch(actions.setSortOption(params.sort));
        dispatch(actions.setFilters(params.filters));
        dispatch(actions.setSearchTerm(params.searchTerm));
      }

      setIsHashInitialized(true);
    }
  }, [isHashInitialized, dispatch]);

  // Update URL hash when filters change
  useEffect(() => {
    if (isHashInitialized) {
      const newHash = filtersToHash(
        timePeriod,
        sortOption,
        filterOptions,
        debouncedSearchTerm
      );
      updateUrlHash(newHash);
    }
  }, [
    timePeriod,
    sortOption,
    filterOptions,
    debouncedSearchTerm,
    isHashInitialized,
  ]);

  const handleShowMoreClick = useCallback(() => {
    // If user is not premium, navigate to pricing page
    if (!isPremium) {
      router.push("/pricing");
      return;
    }
    setCurrentPage((prev) => prev + 1);
  }, [isPremium, router]);

  // Function to handle time period filter change
  const handleTimePeriodChange = (value: string) => {
    setTimePeriod(value);
    dispatch(actions.setTimePeriod(value));
    dispatch(actions.setTopAds(null));
    setCurrentPage(1);
  };

  // Function to handle sort change
  const handleSortChange = (field: string, order: "desc" | "asc") => {
    setSortOption({ field, order });
    dispatch(actions.setSortOption({ field, order }));
    dispatch(actions.setTopAds(null));
    setCurrentPage(1);
  };

  // Function to handle filter change
  const handleFilterChange = (newFilters: VideoFilterState) => {
    setFilterOptions(newFilters);
    dispatch(actions.setFilters(newFilters));
    dispatch(actions.setTopAds(null));
    setCurrentPage(1);
  };

  // Function to handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    dispatch(actions.setSearchTerm(e.target.value));
  };

  // Effect to clear ads when any filter changes (including search)
  useEffect(() => {
    if (isHashInitialized) {
      dispatch(actions.setTopAds(null));
      setCurrentPage(1);
    }
  }, [
    timePeriod,
    sortOption,
    filterOptions,
    debouncedSearchTerm,
    dispatch,
    isHashInitialized,
  ]);

  // Fetch user data on component mount
  useEffect(() => {
    dispatch(userActions.fetchUserMe());
  }, [dispatch]);

  // Fetch data when filters or sort change
  const fetchTopAdsData = useCallback(() => {
    // Only fetch if user is authenticated
    if (!isAuthenticated) return;

    // For non-premium users, only allow fetching page 1
    if (!isPremium && currentPage > 1) return;

    let dateFilter = {};

    if (timePeriod === "30days") {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      dateFilter = { $gte: thirtyDaysAgo.toISOString() };
    } else if (timePeriod === "1year") {
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      dateFilter = { $gte: oneYearAgo.toISOString() };
    }

    dispatch(
      actions.fetchTopAds({
        platforms: filterOptions.platforms,
        pagination: { page: currentPage, pageSize: 10 },
        sort: [{ field: sortOption.field, order: sortOption.order }],
        dateFilter: timePeriod === "all" ? {} : dateFilter,
        searchTerm: debouncedSearchTerm,
      })
    );
  }, [
    dispatch,
    currentPage,
    timePeriod,
    sortOption,
    filterOptions,
    debouncedSearchTerm,
    isPremium,
    isAuthenticated,
  ]);

  // Effect to fetch data when page changes (for pagination)
  useEffect(() => {
    if (isHashInitialized && isAuthenticated && currentPage > 1) {
      fetchTopAdsData();
    }
  }, [currentPage, isHashInitialized, isAuthenticated, fetchTopAdsData]);

  // Effect to fetch data when filters change (after page reset)
  useEffect(() => {
    if (isHashInitialized && isAuthenticated) {
      // Use setTimeout to ensure the page has been reset to 1
      const timeoutId = setTimeout(() => {
        fetchTopAdsData();
      }, 0);
      return () => clearTimeout(timeoutId);
    }
  }, [
    timePeriod,
    sortOption,
    filterOptions,
    debouncedSearchTerm,
    isHashInitialized,
    isAuthenticated,
    dispatch,
  ]);

  // Render login message if not logged in
  const renderLoginMessage = () => {
    return (
      <div className="flex flex-col items-center justify-center py-20 px-4 text-center">
        <div className="bg-secondary/30 p-8 rounded-lg mb-6 max-w-md">
          <LockIcon size={48} className="mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-2xl font-bold mb-3">Login Required</h2>
          <p className="text-muted-foreground mb-6">
            Please log in to access the Top Ads analysis feature. Sign in to
            your account to continue.
          </p>
          <Button
            className="bg-secondary text-primary-foreground font-semibold text-lg 
                      px-6 py-3 rounded-md min-w-[200px]
                      hover:bg-primary/90 transform-gpu 
                      transition duration-200 ease-in-out"
            asChild
          >
            <Link
              href={`/authentication?redirect=${encodeURIComponent(
                router.asPath
              )}`}
            >
              Sign In
            </Link>
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="md:max-w-[80%] mx-auto p-6">
      {userLoading ? (
        <div className="text-center py-8">
          <CircularProgress color="inherit" />
        </div>
      ) : !isAuthenticated || !userData ? (
        // Show login message if user is not authenticated
        renderLoginMessage()
      ) : (
        // Show content for authenticated users (both premium and non-premium)
        <>
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
            <div className="flex flex-wrap items-center gap-3">
              <div className="flex items-center space-x-2">
                <CalendarIcon size={16} className="text-gray-400" />
                <Tabs
                  defaultValue={timePeriod}
                  value={timePeriod}
                  onValueChange={handleTimePeriodChange}
                >
                  <TabsList>
                    <TabsTrigger value="30days">Last 30 days</TabsTrigger>
                    <TabsTrigger value="1year">Last Year</TabsTrigger>
                    <TabsTrigger value="all">All time</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              <div className="relative">
                <SearchIcon
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <Input
                  type="search"
                  placeholder="Search by product or category..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="pl-9 w-[300px]"
                />
              </div>

              <SortAds
                onSortChange={handleSortChange}
                currentSort={sortOption}
              />

              <FilterPanel
                filters={filterOptions}
                onFilterChange={handleFilterChange}
              />
            </div>
          </div>

          {/* Non-premium user notice */}
          {!isPremium && (
            <div className="mb-4 p-3 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-700 rounded-lg">
              <div className="flex items-center gap-2 text-sm">
                <LockIcon
                  size={16}
                  className="text-purple-600 dark:text-purple-400"
                />
                <span className="text-purple-700 dark:text-purple-300">
                  You're viewing the first 10 results.
                  <Link
                    href="/pricing"
                    className="font-medium underline hover:no-underline ml-1"
                  >
                    Upgrade to Premium
                  </Link>{" "}
                  to see all results and unlock advanced features.
                </span>
              </div>
            </div>
          )}

          <div className="relative overflow-x-auto">
            {!topAds ? (
              <div className="text-center py-8">
                <CircularProgress color="inherit" />
              </div>
            ) : topAds.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 text-lg">
                  No ads found matching your criteria
                </p>
              </div>
            ) : (
              <div>
                <TableAds data={topAds || []} />
                <div className="w-full flex justify-center p-4">
                  {!loading ? (
                    <CustomButton
                      id="loading-more-ads"
                      className={`!border-0 font-medium ${
                        !isPremium
                          ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700"
                          : "bg-secondary"
                      } ${
                        (isPremium &&
                          topAdsPagination?.page ===
                            topAdsPagination?.pageCount) ||
                        !topAds.length
                          ? "hidden"
                          : ""
                      }`}
                      onClick={handleShowMoreClick}
                    >
                      {!isPremium ? "Upgrade to See More" : "Show More"}
                    </CustomButton>
                  ) : (
                    <Loading containerClassName="!h-full" />
                  )}
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default TopAds;
