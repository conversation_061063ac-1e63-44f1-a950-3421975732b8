import React, { useState } from 'react';

interface LoginFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  errors: {
    email: string;
    password: string;
    fullName?: string;
    confirmPassword?: string;
  };
  isSignUp: boolean;
  onSubmit: (e: React.FormEvent) => void;
  fullName?: string;
  setFullName?: (name: string) => void;
  confirmPassword?: string;
  setConfirmPassword?: (password: string) => void;
  rememberMe?: boolean;
  setRememberMe?: (remember: boolean) => void;
  termsAccepted?: boolean;
  setTermsAccepted?: (accepted: boolean) => void;
  isLoading?: boolean; // Add isLoading prop
}

const LoginForm: React.FC<LoginFormProps> = ({
  email,
  setEmail,
  password,
  setPassword,
  errors,
  isSignUp,
  onSubmit,
  fullName,
  setFullName,
  confirmPassword,
  setConfirmPassword,
  rememberMe,
  setRememberMe,
  termsAccepted,
  setTermsAccepted,
  isLoading = false, // Default to false if not provided
}) => {
  const [passwordStrength, setPasswordStrength] = useState<string>('');
  
  // Handle explicit button click (will also trigger form's onSubmit)
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Prevent double form submission
    if (isLoading) {
      e.preventDefault();
      return;
    }
  };

  // Handle form enter key submission
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      const form = document.querySelector('form');
      if (form) {
        form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
      }
    }
  };
  
  const checkPasswordStrength = (password: string) => {
    if (!password) {
      setPasswordStrength('');
      return;
    }
    
    // Simple password strength checker
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    const strength = 
      (hasUpperCase ? 1 : 0) +
      (hasLowerCase ? 1 : 0) +
      (hasNumbers ? 1 : 0) +
      (hasSpecialChar ? 1 : 0);
    
    if (password.length < 6) {
      setPasswordStrength('weak');
    } else if (strength <= 2) {
      setPasswordStrength('medium');
    } else if (strength <= 3) {
      setPasswordStrength('strong');
    } else {
      setPasswordStrength('very-strong');
    }
  };

  const getStrengthColor = () => {
    switch (passwordStrength) {
      case 'weak': return 'bg-red-500 dark:bg-red-600';
      case 'medium': return 'bg-yellow-500 dark:bg-yellow-600';
      case 'strong': return 'bg-green-500 dark:bg-green-600';
      case 'very-strong': return 'bg-blue-500 dark:bg-blue-600';
      default: return 'bg-gray-200 dark:bg-gray-700';
    }
  };
  
  const getStrengthWidth = () => {
    switch (passwordStrength) {
      case 'weak': return 'w-1/4';
      case 'medium': return 'w-2/4';
      case 'strong': return 'w-3/4';
      case 'very-strong': return 'w-full';
      default: return 'w-0';
    }
  };

  return (
    <form onSubmit={onSubmit} className="mb-6">
      {isSignUp && (
        <div className="mb-4">
          <label htmlFor="fullName" className="block text-xs font-medium text-foreground mb-1">
            Full Name
          </label>
          <input
            type="text"
            id="fullName"
            className={`w-full px-3 py-2 bg-background border ${
              errors.fullName ? 'border-destructive' : 'border-input'
            } rounded-md focus:outline-none focus:ring-1 focus:ring-primary`}
            value={fullName || ''}
            onChange={(e) => setFullName && setFullName(e.target.value)}
            placeholder="John Doe"
          />
          {errors.fullName && <p className="text-destructive text-xs mt-1">{errors.fullName}</p>}
        </div>
      )}

      <div className="mb-4">
        <label htmlFor="email" className="block text-xs font-medium text-foreground mb-1">
          Email
        </label>
        <input
          type="email"
          id="email"
          className={`w-full px-3 py-2 bg-background border ${
            errors.email ? 'border-destructive' : 'border-input'
          } rounded-md focus:outline-none focus:ring-1 focus:ring-primary`}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="<EMAIL>"
          onKeyDown={handleKeyDown}
          autoComplete="email"
        />
        {errors.email && <p className="text-destructive text-xs mt-1">{errors.email}</p>}
      </div>

      <div className="mb-4">
        <label htmlFor="password" className="block text-xs font-medium text-foreground mb-1">
          Password
        </label>
        <input
          type="password"
          id="password"
          className={`w-full px-3 py-2 bg-background border ${
            errors.password ? 'border-destructive' : 'border-input'
          } rounded-md focus:outline-none focus:ring-1 focus:ring-primary`}
          value={password}
          onChange={(e) => {
            setPassword(e.target.value);
            if (isSignUp) {
              checkPasswordStrength(e.target.value);
            }
          }}
          placeholder="••••••••"
          onKeyDown={handleKeyDown}
          autoComplete={isSignUp ? "new-password" : "current-password"}
        />
        {errors.password && <p className="text-destructive text-xs mt-1">{errors.password}</p>}
        
        {isSignUp && passwordStrength && (
          <div className="mt-2">
            <div className="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full">
              <div className={`h-full rounded-full transition-all ${getStrengthColor()} ${getStrengthWidth()}`}></div>
            </div>
            <p className="text-2xs mt-1 text-muted-foreground">
              Password strength: <span className="font-medium">{passwordStrength}</span>
            </p>
          </div>
        )}
      </div>

      {isSignUp && (
        <div className="mb-4">
          <label htmlFor="confirmPassword" className="block text-xs font-medium text-foreground mb-1">
            Confirm Password
          </label>
          <input
            type="password"
            id="confirmPassword"
            className={`w-full px-3 py-2 bg-background border ${
              errors.confirmPassword ? 'border-destructive' : 'border-input'
            } rounded-md focus:outline-none focus:ring-1 focus:ring-primary`}
            value={confirmPassword || ''}
            onChange={(e) => setConfirmPassword && setConfirmPassword(e.target.value)}
            placeholder="••••••••"
          />
          {errors.confirmPassword && <p className="text-destructive text-xs mt-1">{errors.confirmPassword}</p>}
        </div>
      )}

      {!isSignUp && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="remember"
              className="h-3 w-3 text-primary border-input rounded focus:ring-primary"
              checked={rememberMe || false}
              onChange={(e) => setRememberMe && setRememberMe(e.target.checked)}
            />
            <label htmlFor="remember" className="ml-2 text-[13px] text-foreground">
              Remember me for 30 days
            </label>
          </div>
          <a href="#" className="text-[13px] text-primary hover:underline">
            Forgot password?
          </a>
        </div>
      )}

      {isSignUp && (
        <div className="flex items-start mb-6">
          <div className="flex items-center h-5">
            <input
              type="checkbox"
              id="terms"
              className="h-3 w-3 text-primary border-input rounded focus:ring-primary"
              checked={termsAccepted || false}
              onChange={(e) => setTermsAccepted && setTermsAccepted(e.target.checked)}
            />
          </div>
          <div className="ml-3 text-2xs">
            <label htmlFor="terms" className="text-foreground">
              I agree to the{' '}
              <a href="#" className="text-blue-500 hover:underline">
                Terms and Conditions
              </a>
            </label>
            {errors.fullName && <p className="text-destructive text-xs mt-1">{errors.fullName}</p>}
          </div>
        </div>
      )}

      <button
        type="submit"
        className={`w-full flex items-center justify-center p-3 ${
          isLoading ? 'opacity-70 cursor-not-allowed' : 'hover:shadow-md'
        } bg-background border-border border text-foreground rounded-lg transition-shadow duration-200 text-[16px] cursor-pointer`}
        onClick={handleButtonClick}
        disabled={isLoading}
        aria-busy={isLoading}
      >
        {isLoading && (
          <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        )}
        {isSignUp ? 'Create Account' : 'Sign In'}
      </button>
    </form>
  );
};

export default LoginForm;