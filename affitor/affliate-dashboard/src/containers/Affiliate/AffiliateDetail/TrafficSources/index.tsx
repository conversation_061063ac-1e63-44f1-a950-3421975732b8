import { Loading } from "@/components";
import { selectTrafficWebList, selectErrorTrafficWeb } from "@/features/traffic-web/traffic-web.slice";
import { useSelector } from "react-redux";
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useEffect, useState } from "react";
import { ITrafficSources } from "@/interfaces";
import { useTheme } from "next-themes";



// Define a mapping for source names
const sourceNames: Record<keyof ITrafficSources, string> = {
  mail: "Email",
  direct: "Direct",
  search: "Search",
  social: "Social",
  referrals: "Referrals",
  paid_referrals: "Display Ads"
};

// Define colors for each source
const sourceColors: Record<keyof ITrafficSources, string> = {
  direct: "#195AFE",
  search: "#1DCDF5",
  referrals: "#FC3771",
  social: "#00CD98",
  paid_referrals: "#FEB72B",
  mail: "#C24DFC"
};

export default function TrafficSources() {
  const trafficWebs = useSelector(selectTrafficWebList);
  const error = useSelector(selectErrorTrafficWeb);
  const [chartOptions, setChartOptions] = useState<Highcharts.Options>({});
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  useEffect(() => {
    if (trafficWebs && trafficWebs?.length > 0 && trafficWebs[0]?.traffic_sources) {
      const trafficSources = trafficWebs[0].traffic_sources;
      const chartData = Object.entries(trafficSources)
        .map(([key, value]) => ({
          name: sourceNames[key as keyof ITrafficSources],
          y: value,
          color: sourceColors[key as keyof ITrafficSources],
        }))
        .filter((item) => item.y > 0); // Filter out zero values

      setChartOptions({
        chart: {
          type: "pie",
          backgroundColor: "transparent",
          height: 250,
        },
        title: {
          text: undefined,
        },
        tooltip: {
          pointFormat: "{series.name}: <b>{point.percentage:.1f}%</b>",
          backgroundColor: isDarkMode ? '#333' : '#fff',
          style: {
            color: isDarkMode ? '#fff' : '#333'
          }
        },
        accessibility: {
          point: {
            valueSuffix: "%",
          },
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: "pointer",
            dataLabels: {
              enabled: false,
            },
            showInLegend: false,
          },
        },
        series: [
          {
            name: "Traffic",
            colorByPoint: true,
            data: chartData,
            type: "pie" as const,
          } as Highcharts.SeriesPieOptions,
        ],
        credits: {
          enabled: false,
        },
      });
    }
  }, [trafficWebs, isDarkMode]);

  // Function to format percentage
  const formatPercentage = (value: number) => {
    return (value * 100).toFixed(1) + '%';
  };

  return (
    <div className="p-4 bg-primary shadow rounded-lg h-full">
      <h2 className="text-base md:text-lg font-bold mb-4 text-primary-foreground">Traffic Sources</h2>

      {trafficWebs && trafficWebs.length > 0 && trafficWebs[0]?.traffic_sources ? (
        <div className="flex flex-col md:flex-row">
          {/* Chart Column */}
          <div className="w-full md:w-1/2 mb-4 md:mb-0">
            <HighchartsReact highcharts={Highcharts} options={chartOptions} />
          </div>

          {/* Traffic Sources List */}
          <div className="w-full md:w-1/2 pl-0 md:pl-4">
            <div className="grid grid-cols-1 gap-3">
              {Object.entries(trafficWebs[0].traffic_sources)
                .filter(([_, value]) => value > 0) // Filter out zero values
                .sort(([_, valueA], [__, valueB]) => valueB - valueA) // Sort by value descending
                .map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-1">
                    <div className="flex items-center">
                      <div 
                        className="w-3 h-3 mr-3 rounded-full" 
                        style={{ backgroundColor: sourceColors[key as keyof ITrafficSources] }}
                      />
                      <span className="text-sm text-slate-600 dark:text-slate-300">{sourceNames[key as keyof ITrafficSources]}</span>
                    </div>
                    <span className="text-sm font-semibold text-slate-800 dark:text-white">{formatPercentage(value)}</span>
                  </div>
                ))}
            </div>
          </div>
        </div>
      ) : !error ? (
        <Loading containerClassName="!h-[200px]" />
      ) : (
        <p className="text-xs md:text-sm text-red-400">
          Error loading traffic sources data
        </p>
      )}
    </div>
  );
}
