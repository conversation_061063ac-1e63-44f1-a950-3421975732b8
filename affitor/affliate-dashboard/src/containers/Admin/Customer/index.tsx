"use client";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  actions as referralActions,
  selectReferrals,
  selectReferralsLoading,
  selectReferralsError,
  selectReferralsPagination,
} from "@/features/referral/referral.slice";
import {
  actions as referralCommissionActions,
  selectReferralCommissions,
  selectReferralCommissionsLoading,
  selectReferralCommissionsError,
  selectReferralCommissionsPagination,
} from "@/features/referral-commission/referral-commission.slice";
import { AppDispatch } from "@/store";
import CustomerTab from "./CustomerTab";
import CommisssionTab from "./CommisssionTab";
import TransactionTab from "./TransactionTab";

type CustomerTab = "customers" | "commissions" | "transactions";

export default function AdminCustomer() {
  const dispatch = useDispatch<AppDispatch>();
  const referrals = useSelector(selectReferrals);
  const loading = useSelector(selectReferralsLoading);
  const error = useSelector(selectReferralsError);
  const pagination = useSelector(selectReferralsPagination);

  // Commission tab state from Redux
  const commissions = useSelector(selectReferralCommissions);
  const commissionsLoading = useSelector(selectReferralCommissionsLoading);
  const commissionsError = useSelector(selectReferralCommissionsError);
  const commissionsPagination = useSelector(
    selectReferralCommissionsPagination
  );
  const [commissionsPage, setCommissionsPage] = useState(1);

  const [activeTab, setActiveTab] = useState<CustomerTab>("customers");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [mounted, setMounted] = useState(false);
  const [statusFilter, setStatusFilter] = useState<
    "all" | "conversion" | "lead" | "pending"
  >("all");
  const [sortField, setSortField] = useState<string>("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Fix hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch referrals on component mount and when page changes
  useEffect(() => {
    if (mounted && activeTab === "customers") {
      dispatch(
        referralActions.fetchReferralsRequest({
          page: currentPage,
          pageSize: 10,
          search: searchTerm,
          status: statusFilter === "all" ? "" : statusFilter,
          isAdmin: true,
          sort: `${sortField}:${sortOrder.toUpperCase()}`,
        })
      );
    }
  }, [dispatch, currentPage, mounted, activeTab, sortField, sortOrder]);

  // Fetch commissions when tab is active
  useEffect(() => {
    if (mounted && activeTab === "commissions") {
      dispatch(
        referralCommissionActions.fetchCommissionsRequest({
          page: commissionsPage,
          pageSize: 10,
          isAdmin: true,
        })
      );
    }
  }, [mounted, activeTab, commissionsPage, dispatch]);

  // Handle search with debounce
  useEffect(() => {
    if (!mounted || activeTab !== "customers") return;

    const timer = setTimeout(() => {
      setCurrentPage(1);
      dispatch(
        referralActions.fetchReferralsRequest({
          page: 1,
          pageSize: 10,
          search: searchTerm,
          status: statusFilter === "all" ? "" : statusFilter,
          isAdmin: true,
          sort: `${sortField}:${sortOrder.toUpperCase()}`,
        })
      );
    }, 500);

    return () => clearTimeout(timer);
  }, [
    dispatch,
    searchTerm,
    statusFilter,
    mounted,
    activeTab,
    sortField,
    sortOrder,
  ]);

  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle sort order if same field
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      // Set new field with default desc order
      setSortField(field);
      setSortOrder("desc");
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="p-3 sm:p-4 lg:p-6 bg-gray-50 dark:bg-gray-900 min-h-full">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Customers
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage customers, commissions, and transactions
            </p>
          </div>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            Create customer
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <nav className="flex space-x-1 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm border border-gray-200 dark:border-gray-700">
          {[
            {
              key: "customers",
              label: "Customers",
              count: pagination?.total || 0,
            },
            { key: "commissions", label: "Commissions", count: 4 },
            { key: "transactions", label: "Transactions", count: 5 },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as CustomerTab)}
              className={`flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.key
                  ? "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
                  : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "customers" && (
        <CustomerTab
          referrals={referrals}
          loading={loading}
          error={error}
          pagination={pagination}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          sortField={sortField}
          sortOrder={sortOrder}
          onSort={handleSort}
        />
      )}
      {activeTab === "commissions" && (
        <CommisssionTab
          commissions={commissions}
          commissionsLoading={commissionsLoading}
          commissionsError={commissionsError}
          commissionsPagination={commissionsPagination}
          commissionsPage={commissionsPage}
          setCommissionsPage={setCommissionsPage}
        />
      )}
      {activeTab === "transactions" && <TransactionTab />}
    </div>
  );
}
