import { call, put, takeEvery, select } from 'redux-saga/effects';
import { PayloadAction } from '@reduxjs/toolkit';
import { actions, UserPromptAction, selectSessionId } from './aiscript.slice';
import axios from 'axios';
import { handleApiError } from '@/utils/error-handler';

// Set a higher timeout for API requests (30 seconds instead of default 10)
const API_TIMEOUT = 30000;

// Helper function to handle axios errors
function* handleAxiosError(error: any): Generator<any, boolean, any> {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    return yield call(handleApiError, new Response(null, {
      status: error.response.status,
      statusText: error.response.statusText
    }));
  }
  return false;
}

function* handleSendMessage(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    // Set loading state
    yield put(actions.setLoading(true));
    
    // Get user message from action payload
    const message = action.payload;
    
    // Get sessionId from state
    const sessionId = yield select(selectSessionId);
    console.log("LOG-sessionId 3", sessionId);
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setError('Authentication required'));
      return;
    }
    
    // Call the Next.js API route with increased timeout
    const response = yield call(axios.post, '/api/aiscript', 
      { 
        message,
        sessionId,  // Include sessionId in the request if available
        // No promptId for regular messages
      },
      { 
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT // Increase timeout to 30 seconds
      }
    );
    console.log('LOG-response', response);
    // Handle response
    if (response && response.data) {
      // Add AI response to messages with copyable flag
      yield put(actions.setMessage({ 
        type: 'ai', 
        content: response.data.message,
        copyable: true  // Add copyable flag to make AI responses copyable
      }));
      
      // Add any quick replies if provided
      if (response.data.suggestions && response.data.suggestions.length > 0) {}

      // Put the session_id to the AIScript state
      if (response.data.session_id)
        yield put(actions.setSessionId(response.data.session_id));
    } else {
      // Handle unexpected response format
      yield put(actions.setError("Received an invalid response format from the server"));
    }
  } catch (error: any) {
    // Handle specific error cases
    if (!(yield call(handleAxiosError, error))) {
      yield put(actions.setError("Failed to send message"));
    }
  } finally {
    // Always reset loading state
    yield put(actions.setLoading(false));
  }
}

// Handler for user prompts
function* handleSendUserPrompt(
  action: PayloadAction<UserPromptAction>
): Generator<any, void, any> {
  try {
    // Set loading state
    yield put(actions.setLoading(true));
    
    // Get data from the action payload
    const { content, promptId } = action.payload;
    
    // Get sessionId from state
    const sessionId = yield select(selectSessionId);

    console.log('LOG-sessionId 1', sessionId);
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setError('Authentication required'));
      return;
    }
    
    // Call the Next.js API route with the full prompt content and promptId
    const response = yield call(axios.post, '/api/aiscript', 
      { 
        message: content,
        promptId,  // Include the promptId if available
        sessionId, // Include sessionId in the request if available
      },
      { 
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT // Increase timeout to 30 seconds
      }
    );
    
    console.log("AIScript response:", response);
    // Handle response
    if (response && response.data) {
      yield put(actions.setMessage({ 
        type: 'ai', 
        copyable: true,
        content: response.data.message
      }));
      
      // Update sessionId if it's in the response
      if (response.data.session_id) {
        yield put(actions.setSessionId(response.data.session_id));
      }
    } else {
      yield put(actions.setError("Received an invalid response format from the server"));
    }
  } catch (error: any) {
    // Handle specific error cases
    if (!(yield call(handleAxiosError, error))) {
      yield put(actions.setError("Failed to process prompt"));
    }
  } finally {
    yield put(actions.setLoading(false));
  }
}

function* handleEndSession(): Generator<any, void, any> {
  try {
    // Get sessionId from state
    const sessionId = yield select(selectSessionId);

    console.log('LOG-sessionId 2', sessionId);
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      console.log("No authentication token available");
      return;
    }
    
    // Call the endpoint for ending sessions
    yield call(axios.post, '/api/aiscript/end', 
      { 
        sessionId // Include sessionId in the request if available
      },
      { 
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT // Increase timeout to 30 seconds
      }
    );

    // clear the sessionId from the state
    yield put(actions.setSessionId(null));
    
    console.log("AIScript session ended successfully");
  } catch (error: any) {
    console.error("Error ending AIScript session:", error);
  }
}

function* handleFetchPrompts(): Generator<any, void, any> {
  try {
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setPromptsError('Authentication required'));
      return;
    }
    
    // Call the prompts API endpoint
    const response = yield call(axios.get, '/api/prompts', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT // Increase timeout to 30 seconds
    });
    
    // Check if the response contains data
    if (response?.data?.data) {
      yield put(actions.setPrompts(response.data.data));
    } else {
      yield put(actions.setPromptsError('Invalid response format'));
    }
  } catch (error: any) {
    // Handle specific error cases
    if (!(yield call(handleAxiosError, error))) {
      yield put(actions.setPromptsError("Failed to fetch prompt templates"));
    }
  }
}

function* handleOpenAIScript(): Generator<any, void, any> {
  // When opening the AIScript
}

export default function* aiscriptSaga() {
  yield takeEvery(actions.sendMessage.type, handleSendMessage);
  yield takeEvery(actions.sendUserPrompt.type, handleSendUserPrompt);
  yield takeEvery(actions.endSession.type, handleEndSession);
  yield takeEvery(actions.fetchPrompts.type, handleFetchPrompts);
  yield takeEvery(actions.openAIScript.type, handleOpenAIScript);
}
