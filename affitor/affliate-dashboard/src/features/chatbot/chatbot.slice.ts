import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/store';

// Define the structure for quick replies
export interface QuickReply {
  label: string;
  content: string;
}

export interface Message {
  type: 'user' | 'ai';
  content: string;
  quickReplies?: QuickReply[]; // Only support QuickReply[] type
  copyable?: boolean; // Add flag to indicate if message should have copy button
}

interface ChatbotState {
  messages: Message[];
  loading: boolean;
  error: string | null;
  isOpen: boolean;
  quickReplies: QuickReply[]; // Use QuickReply[] instead of string[]
}

const initialState: ChatbotState = {
  messages: [
    {
      type: 'ai',
      content: "Hi there! I'm Affiliate<PERSON><PERSON>, your AI assistant for finding the best affiliate programs. How can I help you today?"
    }
  ],
  loading: false,
  error: null,
  isOpen: false,
  quickReplies: [] // Initialize as empty array
};

const chatbotSlice = createSlice({
  name: 'chatbot',
  initialState,
  reducers: {
    // Async action trigger
    sendMessage: (state, action: PayloadAction<string>) => {
      // Add user message
      state.messages.push({
        type: 'user',
        content: action.payload
      });
      state.error = null;
    },
    
    // Sync actions
    setMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },
    
    setMessageWithQuickReplies: (state, action: PayloadAction<{content: string, quickReplies: QuickReply[]}>) => {
      state.messages.push({
        type: 'ai',
        content: action.payload.content,
        quickReplies: action.payload.quickReplies
      });
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      
      // Add error message if provided
      if (action.payload) {
        state.messages.push({
          type: 'ai',
          content: "I'm having trouble connecting. Please try again later."
        });
      }
    },
    
    clearMessages: (state) => {
      state.messages = [state.messages[0]]; // Keep only the welcome message
    },
    
    // ChatBot visibility actions
    openChatBot: (state) => {
      state.isOpen = true;
    },
    
    closeChatBot: (state) => {
      state.isOpen = false;
    },
    
    toggleChatBot: (state) => {
      state.isOpen = !state.isOpen;
    },
    
    // Update to use QuickReply[]
    setQuickReplies: (state, action: PayloadAction<QuickReply[]>) => {
      state.quickReplies = action.payload;
    },
    
    // Keep endSession action as a trigger for saga
    endSession: (state) => {
      // This will be handled by saga
      // Close the chatbot when ending session
      state.isOpen = false;
      state.quickReplies = []; // Clear quick replies when ending session
    }
  }
});

export const { actions, reducer } = chatbotSlice;

// Export for backward compatibility
export const { sendMessage } = actions;
export default reducer;

// Selectors
const selectChatbotState = (state: RootState) => state.chatbot;

export const selectChatbotMessages = createSelector(
  [selectChatbotState],
  (chatbotState) => chatbotState.messages
);

export const selectChatbotLoading = createSelector(
  [selectChatbotState],
  (chatbotState) => chatbotState.loading
);

export const selectChatbotError = createSelector(
  [selectChatbotState],
  (chatbotState) => chatbotState.error
);

export const selectIsChatbotOpen = createSelector(
  [selectChatbotState],
  (chatbotState) => chatbotState.isOpen
);

// Add selector for quick replies
export const selectChatbotQuickReplies = createSelector(
  [selectChatbotState],
  (chatbotState) => chatbotState.quickReplies
);
