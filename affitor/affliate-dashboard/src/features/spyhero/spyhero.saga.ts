import { call, put, takeLatest } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import { actions } from "./spyhero.slice";
import { actions as authActions } from "@/features/auth/auth.slice";
import axios from "axios";

function* searchAdsSaga(
  action: PayloadAction<{ keyword: string; platform: string }>
): Generator<any, void, any> {
  try {
    const { keyword, platform } = action.payload;
    
    // Get token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    
    console.log(`Searching ads with keyword: "${keyword}" on platform: ${platform}`);

    // Make request to proxy API instead of StrapiClient
    const response: any = yield call(
      axios.post,
      "/api/spyhero/search-ad",
      { keyword, platform },
      {
        headers: {
          "Content-Type": "application/json",
          ...(token && { Authorization: `Bearer ${token}` }),
        },
      }
    );

    const data = response.data;

    if (data.success) {
      yield put(
        actions.searchAdsSuccess({
          ads: data.data.ads,
          totalCount: data.data.count,
          lastCrawled: data.data.crawledAt,
        })
      );
      console.log(`Found ${data.data.count} ads for "${keyword}"`);
    } else {
      yield put(actions.searchAdsFailure(data.message || "Failed to search ads"));
    }
  } catch (error: any) {
    console.error("Search ads saga error:", error);
    
    // Handle specific error status codes
    if (error.response?.status === 401) {
      yield put(
        authActions.setRequireAuth("You need to sign in to access this resource.")
      );
      yield put(actions.searchAdsFailure("Authentication required"));
    } else if (error.response?.status === 429) {
      yield put(
        authActions.setRequireUpgrade("Rate limit exceeded. Please upgrade your plan to continue.")
      );
      yield put(actions.searchAdsFailure("Rate limit exceeded"));
    } else if (error.response?.status === 403) {
      yield put(actions.searchAdsFailure("Access denied. Please check your permissions."));
    } else if (error.response?.status >= 500) {
      yield put(actions.searchAdsFailure("Server error. Please try again later."));
    } else {
      // General error handling
      yield put(
        actions.searchAdsFailure(
          error.response?.data?.message || error.message || "An error occurred while searching ads"
        )
      );
    }
  } finally {
    yield put(actions.setLoading(false));
  }
}

export default function* spyheroSaga() {
  yield takeLatest(actions.searchAdsRequest.type, searchAdsSaga);
}