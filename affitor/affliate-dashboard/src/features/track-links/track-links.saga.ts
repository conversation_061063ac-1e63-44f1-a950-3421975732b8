import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./track-links.slice";
import { handleApiError } from "@/utils/error-handler";
import { PayloadAction } from "@reduxjs/toolkit";

function* fetchOverviewSaga(): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    const headers: any = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response: any = yield call(fetch, `/api/track-links/overview`, {
      method: "GET",
      headers,
    });

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }

      yield put(
        actions.fetchOverviewFailure(
          `Request failed with status ${response.status}`
        )
      );
      return;
    }

    const data = yield response.json();

    if (!data) {
      yield put(actions.fetchOverviewFailure("Invalid response structure"));
      return;
    }

    yield put(actions.fetchOverviewSuccess(data));
  } catch (error: any) {
    yield put(
      actions.fetchOverviewFailure(
        error.message || "Failed to fetch track links overview"
      )
    );
  }
}

function* fetchPerformanceOverviewSaga(
  action: PayloadAction<{ period: string }>
): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
    const { period } = action.payload;

    const headers: any = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response: any = yield call(
      fetch,
      `/api/track-links/performance-overview?period=${period}`,
      {
        method: "GET",
        headers,
      }
    );

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }

      yield put(
        actions.fetchPerformanceOverviewFailure(
          `Request failed with status ${response.status}`
        )
      );
      return;
    }

    const data = yield response.json();

    if (!data) {
      yield put(
        actions.fetchPerformanceOverviewFailure("Invalid response structure")
      );
      return;
    }

    yield put(actions.fetchPerformanceOverviewSuccess(data));
  } catch (error: any) {
    yield put(
      actions.fetchPerformanceOverviewFailure(
        error.message || "Failed to fetch track links performance overview"
      )
    );
  }
}

export default function* trackLinksSaga() {
  yield takeEvery(actions.fetchOverviewRequest.type, fetchOverviewSaga);
  yield takeEvery(
    actions.fetchPerformanceOverviewRequest.type,
    fetchPerformanceOverviewSaga
  );
}
