import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface ReferralUser {
  id: number;
  documentId: string;
  username?: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

export interface ReferralReferrer {
  id: number;
  documentId: string;
  referral_code: string;
  user?: {
    first_name: string;
    last_name: string;
    email: string;
    username: string;
  };
}

export interface Referral {
  id: number;
  documentId: string;
  referral_status: "lead" | "conversion" | "pending";
  total_paid: number;
  total_commission: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  user: ReferralUser;
  referrer?: ReferralReferrer;
}

export interface ReferralPagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

interface ReferralState {
  referrals: Referral[];
  loading: boolean;
  error: string | null;
  pagination: ReferralPagination | null;
}

const initialState: ReferralState = {
  referrals: [],
  loading: false,
  error: null,
  pagination: null,
};

const referralSlice = createSlice({
  name: "referral",
  initialState,
  reducers: {
    // Fetch referrals actions
    fetchReferralsRequest: (
      state,
      action: PayloadAction<{
        page?: number;
        pageSize?: number;
        search?: string;
        status?: string;
        isAdmin?: boolean;
        sort?: string;
      }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    fetchReferralsSuccess: (
      state,
      action: PayloadAction<{
        data: Referral[];
        meta: { pagination: ReferralPagination };
      }>
    ) => {
      state.loading = false;
      state.referrals = action.payload.data;
      state.pagination = action.payload.meta.pagination;
      state.error = null;
    },
    fetchReferralsFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Clear actions
    clearReferrals: (state) => {
      state.referrals = [];
      state.pagination = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const actions = referralSlice.actions;
export const reducer = referralSlice.reducer;

// Selectors
export const selectReferrals = (state: any) => state.referral.referrals;
export const selectReferralsLoading = (state: any) => state.referral.loading;
export const selectReferralsError = (state: any) => state.referral.error;
export const selectReferralsPagination = (state: any) =>
  state.referral.pagination;
