import { call, put, takeEvery, select } from "redux-saga/effects";
import { actions } from "./top-videos.slice";
import { PayloadAction } from "@reduxjs/toolkit";
import { IPagination, ISort } from "@/interfaces";
import qs from "qs";
import { RootState } from "@/store";
import { handleApiError } from "@/utils/error-handler";

function* handleFetch(
  action: PayloadAction<{
    platforms: string[];
    pagination: IPagination;
    sort: ISort[];
    dateFilter?: any;
    searchTerm?: string;
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));

    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Get current state for filters and sort
    const state: RootState = yield select();
    const { sort, filters, searchTerm } = state.topVideos;

    const { pagination, dateFilter = {} } = action.payload;

    // Smart platform selection with fallback chain
    const selectedPlatforms =
      filters.platforms.length > 0
        ? filters.platforms
        : action.payload.platforms.length > 0
        ? action.payload.platforms
        : ["youtube", "tiktok"];

    // Build filters more efficiently
    const buildQueryFilters = () => {
      const queryFilters: any = {
        platform: { $in: selectedPlatforms },
      };

      // Apply date filter if available
      if (dateFilter && Object.keys(dateFilter).length > 0) {
        queryFilters.published_from = dateFilter;
      }

      // Apply category filter if available
      if (filters.categories.length > 0) {
        if (!queryFilters.affiliate) {
          queryFilters.affiliate = {};
        }
        queryFilters.affiliate.categories = { $in: filters.categories };
      }

      // Apply views filter if available
      if (filters.minViews) {
        const views = parseInt(filters.minViews);
        if (!isNaN(views)) {
          queryFilters.views = { $gte: views };
        }
      }

      // Apply search term filter with improved logic
      const searchTermToUse = action.payload.searchTerm || searchTerm;
      if (searchTermToUse && searchTermToUse.trim()) {
        const trimmedSearch = searchTermToUse.trim();
        console.log(`Applying search filter: "${trimmedSearch}"`);
        queryFilters.$or = [
          { affiliate: { name: { $containsi: trimmedSearch } } },
          {
            affiliate: { categories: { name: { $containsi: trimmedSearch } } },
          },
        ];
      }

      return queryFilters;
    };

    // Generate filters
    const queryFilters = buildQueryFilters();

    const populateConfig = {
      affiliate: {
        populate: {
          image: { fields: ["id", "url"] },
          // commission: { fields: ["id", "max_percentage", "title"] },
          industry: { fields: ["id", "name", "slug"] },
          categories: { fields: ["id", "name", "slug"] },
        },
        fields: ["id", "name", "url", "slug"],
      },
    };

    // Build sort params with improved null handling
    const sortParams = sort?.field
      ? [`${sort.field}:${sort.order || "desc"}`]
      : action.payload.sort?.length > 0
      ? [
          `${action.payload.sort[0].field}:${
            action.payload.sort[0].order || "desc"
          }`,
        ]
      : ["published_from:desc"]; // Default sort

    const query = qs.stringify(
      {
        filters: queryFilters,
        pagination: {
          page: pagination.page || 1,
          pageSize: pagination.pageSize || 10,
        },
        sort: sortParams,
        populate: populateConfig,
      },
      {
        encodeValuesOnly: true,
      }
    );

    console.log(
      `Fetching videos with: platforms=${selectedPlatforms.join(",")}, page=${
        pagination.page
      }, filters=${Object.keys(queryFilters).length}`
    );

    const response: any = yield call(fetch, `/api/social-listenings?${query}`, {
      method: "GET",
      headers,
    });

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        // set is loading fail
        yield put(actions.setLoading(false));
        return; // Error was handled
      }

      yield put(
        actions.setError(`Request failed with status ${response.status}`)
      );
      return;
    }

    const jsonResponse = yield response.json();
    const { data, meta } = jsonResponse;

    if (!data || !Array.isArray(data)) {
      console.error("Invalid response data structure:", jsonResponse);
      yield put(
        actions.setError("Invalid response: missing or malformed data")
      );
      return;
    }

    if (!meta || !meta.pagination) {
      console.warn("Response missing pagination metadata");
    }

    yield put(actions.setTopVideos(data));
    if (meta?.pagination) {
      yield put(actions.setPagination(meta.pagination));
    }

    // Clear any previous errors
    yield put(actions.setError(null));

    console.log(`Loaded ${data.length} videos successfully`);
  } catch (error: any) {
    console.error("Error fetching top videos:", error);
    const errorMessage = error?.message || "Failed to fetch top videos";
    yield put(actions.setError(errorMessage));
  } finally {
    yield put(actions.setLoading(false));
  }
}

export default function* topVideosSaga() {
  yield takeEvery(actions.fetchTopVideos.type, handleFetch);
}
