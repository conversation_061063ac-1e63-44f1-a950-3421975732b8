"use client"

import { useTheme } from "next-themes"

export function ModeToggle() {
  const { theme, setTheme } = useTheme()
  return (
    <button
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      className="w-5 md:w-10 h-10 rounded-lg flex items-center justify-start md:justify-center hover:bg-muted transition-colors"
    >
      {theme === "light" ? (
        <i className="fas fa-moon"></i>
      ) : (
        <i className="fas fa-sun"></i>
      )}
    </button>
  )
}
