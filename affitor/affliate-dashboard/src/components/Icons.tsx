export function CalendarSVG({className = "", fill = "currentColor"}) {
	return (
    <svg
      fill={fill}
      version="1.1"
      width="15"
      height="15"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 612 612"
      xmlSpace="preserve"
      className={className}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        {" "}
        <g>
          {" "}
          <path d="M534.219,72.689v-0.177h-35.172V19.125C499.047,8.563,490.484,0,479.922,0h-41.438c-10.562,0-19.125,8.563-19.125,19.125 v53.392h-32.871H224.724h-31.676V19.125C193.048,8.563,184.484,0,173.923,0h-41.439c-10.562,0-19.125,8.563-19.125,19.125v53.392 H80.884C33.87,72.517,0,106.77,0,149.017V535.5C0,577.747,34.253,612,76.5,612h459c42.246,0,76.5-34.253,76.5-76.5V149.017 C612.004,107.937,579.947,74.511,534.219,72.689z M152.211,558.211c0,5.273-4.289,9.562-9.562,9.562H47.817 c-5.274,0-9.562-4.289-9.562-9.562v-76.299c0-5.274,4.289-9.562,9.562-9.562h94.826c5.274,0,9.562,4.288,9.562,9.562v76.299 H152.211z M152.211,424.929c0,5.273-4.289,9.562-9.562,9.562H47.817c-5.274,0-9.562-4.289-9.562-9.562v-57.375 c0-5.273,4.289-9.562,9.562-9.562h94.826c5.274,0,9.562,4.289,9.562,9.562v57.375H152.211z M152.211,309.586 c0,5.273-4.289,9.562-9.562,9.562H47.817c-5.274,0-9.562-4.289-9.562-9.562v-77.695c0-5.274,4.289-9.562,9.562-9.562h94.826 c5.274,0,9.562,4.289,9.562,9.562v77.695H152.211z M286.88,558.211c0,5.273-4.289,9.562-9.562,9.562h-76.5 c-5.274,0-9.562-4.289-9.562-9.562v-76.299c0-5.274,4.289-9.562,9.562-9.562h76.5c5.273,0,9.562,4.288,9.562,9.562V558.211z M286.88,424.929c0,5.273-4.289,9.562-9.562,9.562h-76.5c-5.274,0-9.562-4.289-9.562-9.562v-57.375 c0-5.273,4.289-9.562,9.562-9.562h76.5c5.273,0,9.562,4.289,9.562,9.562V424.929z M286.88,309.586c0,5.273-4.289,9.562-9.562,9.562 h-76.5c-5.274,0-9.562-4.289-9.562-9.562v-77.695c0-5.274,4.289-9.562,9.562-9.562h76.5c5.273,0,9.562,4.289,9.562,9.562V309.586z M420.754,558.211c0,5.273-4.287,9.562-9.562,9.562H335.49c-5.273,0-9.562-4.289-9.562-9.562v-76.299 c0-5.274,4.289-9.562,9.562-9.562h75.701c5.275,0,9.562,4.288,9.562,9.562V558.211z M420.754,424.929 c0,5.273-4.287,9.562-9.562,9.562H335.49c-5.273,0-9.562-4.289-9.562-9.562v-57.375c0-5.273,4.289-9.562,9.562-9.562h75.701 c5.275,0,9.562,4.289,9.562,9.562V424.929z M420.754,309.586c0,5.273-4.287,9.562-9.562,9.562H335.49 c-5.273,0-9.562-4.289-9.562-9.562v-77.695c0-5.274,4.289-9.562,9.562-9.562h75.701c5.275,0,9.562,4.289,9.562,9.562V309.586z M572.961,558.211c0,5.273-4.289,9.562-9.562,9.562h-93.637c-5.273,0-9.562-4.289-9.562-9.562v-76.299 c0-5.274,4.289-9.562,9.562-9.562h93.633c5.273,0,9.562,4.288,9.562,9.562v76.299H572.961z M572.961,424.929 c0,5.273-4.289,9.562-9.562,9.562h-93.637c-5.273,0-9.562-4.289-9.562-9.562v-57.375c0-5.273,4.289-9.562,9.562-9.562h93.633 c5.273,0,9.562,4.289,9.562,9.562v57.375H572.961z M572.961,309.586c0,5.273-4.289,9.562-9.562,9.562h-93.637 c-5.273,0-9.562-4.289-9.562-9.562v-77.695c0-5.274,4.289-9.562,9.562-9.562h93.633c5.273,0,9.562,4.289,9.562,9.562v77.695 H572.961z"></path>{" "}
        </g>{" "}
      </g>
    </svg>
  );
}

export function CheckIcon({className = ""}) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      width="15"
      height="15"
      viewBox="0,0,256,256"
    >
      <g
        fill="#20c997"
        fillRule="nonzero"
        stroke="none"
        strokeWidth="1"
        strokeLinecap="butt"
        strokeLinejoin="miter"
        strokeMiterlimit="10"
        strokeDasharray=""
        strokeDashoffset="0"
        fontFamily="none"
        fontWeight="none"
        fontSize="none"
        textAnchor="none"
      >
        <g transform="scale(10.66667,10.66667)">
          <path d="M5,3c-1.103,0 -2,0.897 -2,2v14c0,1.103 0.897,2 2,2h14c1.103,0 2,-0.897 2,-2v-9.75781l-2,2l0.00195,7.75781h-14.00195v-14h11.75781l2,-2zM21.29297,3.29297l-10.29297,10.29297l-3.29297,-3.29297l-1.41406,1.41406l4.70703,4.70703l11.70703,-11.70703z"></path>
        </g>
      </g>
    </svg>
  );
}

export function ArrowUpSVG({ className = "w-[25px]" }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" 
      viewBox="0 0 24 24"
      width="15"
      height="15"
      className={className}
      >
      <path
        d="m16.707 13.293-4-4a1 1 0 0 0-1.414 0l-4 4A1 1 0 0 0 8 15h8a1 1 0 0 0 .707-1.707z"
        fill="currentColor"
        data-name="Up"
      />
    </svg>
  );
}

export function EyeSVG({ className = "w-[25px]" }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlSpace="preserve"
      viewBox="0 0 48 48"
      id="eye"
      className={className}
    >
      <symbol id="New_Symbol_14" viewBox="-6.5 -6.5 13 13">
        <path
          fill="#FFD4C3"
          stroke="#504B46"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-miterlimit="10"
          d="M0-6c2.2 0 4.1 1.5 4.7 3.5C6.3-2.5 6.4 0 5 0v1c0 2.8-2.2 5-5 5s-5-2.2-5-5V0c-1.4 0-1.3-2.5.2-2.5C-4.1-4.5-2.2-6 0-6z"
          style={{ fill: "#ffd4c3", stroke: "#504b46", strokeLinecap: "round", strokeLinejoin: "round", strokeMiterlimit: 10 }}
        ></path>
        <circle cx="-1.6" cy="-.1" r=".1" style={{ fill: "#ffc258" }}></circle>
        <path
          d="M-1.6.5c-.3 0-.6-.3-.6-.6s.2-.7.6-.7c.3 0 .6.3.6.7s-.3.6-.6.6z"
          style={{ fill: "#4f4b45" }}
        ></path>
        <circle cx="1.6" cy="-.1" r=".1" style={{ fill: "#ffc258" }}></circle>
        <path
          d="M1.6.5C1.3.5 1 .2 1-.1s.3-.6.6-.6.6.3.6.6-.2.6-.6.6z"
          fill="#4f4b45"
        ></path>
        <circle cx="-3" cy="-1.5" r=".5" style={{ fill: "#fabfa5" }}></circle>
        <circle cx="3" cy="-1.5" r=".5" style={{ fill: "#fabfa5" }}></circle>
        <path
          fill="none"
          stroke="#504B46"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-miterlimit="10"
          d="M-1.2-3c.8-.5 1.7-.5 2.5 0"
          style={{ fill: "none", stroke: "#504b46", strokeLinecap: "round", strokeLinejoin: "round", strokeMiterlimit: 10 }}
        ></path>
      </symbol>
      <g id="Icons">
        <g>
          <ellipse
            id="eye-shadow"
            cx="24"
            cy="38.2"
            fill="#45413C"
            opacity=".15"
            rx="13.6"
            ry="1.9"
            style={{ opacity: 0.15, fill: "#45413c" }}
          ></ellipse>
          <path
            id="eye-white"
            fill="#FFF"
            d="M45.8 20c0 3.7-8 12.1-21.8 12.1S2.2 23.8 2.2 20 10.1 8 24 8s21.8 8.3 21.8 12z"
            style={{ fill: "#fff" }}
          ></path>
          <path
            id="eye-gray"
            fill="#E0E0E0"
            d="M24 13.8c10.9 0 18.1 5.1 20.7 9.1.7-1.1 1.1-2.1 1.1-2.9 0-3.7-7.9-12-21.8-12S2.2 16.3 2.2 20c0 .8.4 1.8 1.1 2.9 2.6-3.9 9.8-9.1 20.7-9.1z"
            style={{ fill: "#e0e0e0" }}
          ></path>
          <path
            id="eye-outline"
            d="M45.8 20c0 3.7-8 12.1-21.8 12.1S2.2 23.8 2.2 20 10.1 8 24 8s21.8 8.3 21.8 12z"
            style={{ fill: "none", stroke: "#45413c", strokeLinecap: "round", strokeLinejoin: "round", strokeMiterlimit: 10 }}
          ></path>
          <path
            id="eye-pupil"
            fill="#656769"
            stroke="#45413C"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-miterlimit="10"
            d="M24 10.8c13.1 0 20.7 8.2 20.8 12 .7-1.1 1-2 1-2.8 0-3.7-7.9-12-21.8-12S2.2 16.3 2.2 20c0 .8.3 1.7 1 2.8.1-3.7 7.7-12 20.8-12z"
            style={{ fill: "#656769", stroke: "#45413c", strokeLinecap: "round", strokeLinejoin: "round", strokeMiterlimit: 10 }}
          ></path>
          <circle
            id="eye-iris"
            cx="24"
            cy="20"
            r="9"
            fill="#B89558"
            style={{ fill: "#b89558" }}
          ></circle>
          <path
            id="eye-iris-shadow"
            fill="#947746"
            d="M30.2 13.6c1.1 1.4 1.8 3.2 1.8 5.1 0 4.4-3.6 8.1-8.1 8.1s-8.1-3.6-8.1-8.1c0-1.9.7-3.7 1.8-5.1-1.5 1.6-2.6 3.9-2.6 6.4 0 5 4 9 9 9s9-4 9-9c0-2.5-1.1-4.8-2.8-6.4z"
            style={{ fill: "#947746" }}
          ></path>
          <circle
            id="eye-iris-outline"
            cx="24"
            cy="20"
            r="9"
            style={{ fill: "none", stroke: "#45413c", strokeLinecap: "round", strokeLinejoin: "round", strokeMiterlimit: 10 }}
          ></circle>
          <circle
            id="eye-pupil-outline"
            cx="24"
            cy="20"
            r="4"
            fill="#525252"
            stroke="#45413C"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-miterlimit="10"
            style={{ fill: "#525252", stroke: "#45413c", strokeLinecap: "round", strokeLinejoin: "round", strokeMiterlimit: 10 }}
          ></circle>
          <circle
            id="eye-highlight"
            cx="20.6"
            cy="15.8"
            r="3.1"
            fill="#FFF"
            stroke="#45413C"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-miterlimit="10"
            style={{ fill: "#fff", stroke: "#45413c", strokeLinecap: "round", strokeLinejoin: "round", strokeMiterlimit: 10 }}
          ></circle>
        </g>
      </g>
    </svg>
  );
}