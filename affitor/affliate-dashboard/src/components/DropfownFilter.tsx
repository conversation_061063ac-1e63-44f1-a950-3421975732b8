export function DropdownFilter({
  name,
  options,
  onChange,
}: {
  name: string;
  options: {
    value: string;
    label: string;
  }[];
  onChange: (value: string) => void;
}) {
  return (
    <select
      onChange={(e) => onChange(e.target.value)}
      className="px-2 py-1 rounded-lg cursor-pointer bg-secondary h-[40px]"
    >
      {options.map((option, index) => (
        <option key={option.value} value={option.value}>
          {!index ? name : ""} {option.label}
        </option>
      ))}
    </select>
  );
}
