import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { method } = req;

  if (method === "GET") {
    return handleGetPayouts(req, res);
  } else if (method === "POST") {
    return handleCreatePayout(req, res);
  } else {
    return res.status(405).json({ error: "Method not allowed" });
  }
}

async function handleGetPayouts(req: NextApiRequest, res: NextApiResponse) {

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Extract query parameters
    const { status, page, pageSize, search } = req.query;

    const query = {
      status: status as "pending" | "approved" | "completed" | undefined,
      page: page ? parseInt(page as string) : undefined,
      pageSize: pageSize ? parseInt(pageSize as string) : undefined,
      search: search as string | undefined,
    };

    // Call StrapiAdminClient to get payouts
    const data = (await StrapiAdminClient.getAdminPayouts(
      query,
      token!
    )) as any;

    // Transform the data to match our expected format
    const transformedData = {
      data:
        data.results?.map((payout: any) => ({
          id: payout.id,
          documentId: payout.documentId,
          payout_status: payout.payout_status,
          payment_method: payout.method, // Map 'method' to 'payment_method'
          commission_cycle: "monthly", // Default value since not in API response
          amount: payout.amount,
          processing_fee: payout.processing_fee,
          payout_date: payout.payout_date,
          createdAt: payout.createdAt,
          updatedAt: payout.updatedAt,
          publishedAt: payout.publishedAt,
          // Map referrer to partner structure expected by frontend
          partner: payout.referrer
            ? {
                id: payout.referrer.id,
                documentId: payout.referrer.documentId,
                referral_code: payout.referrer.referral_code,
                referrer_status: payout.referrer.referrer_status,
                // Map referrer's user data to partner user structure
                user: payout.referrer.user
                  ? {
                      id: payout.referrer.user.id,
                      username:
                        payout.referrer.user.username ||
                        payout.referrer.referral_code,
                      email: payout.referrer.user.email,
                      first_name: payout.referrer.user.first_name,
                      last_name: payout.referrer.user.last_name,
                    }
                  : {
                      id: payout.referrer.id,
                      username:
                        payout.referrer.referral_code ||
                        `user-${payout.referrer.id}`,
                      email: `referrer-${payout.referrer.id}@example.com`,
                      first_name: "Unknown",
                      last_name: "User",
                    },
              }
            : null,
          referrer: payout.referrer, // Keep original referrer data
          status: payout.status,
        })) || [],
      meta: {
        pagination: data.pagination || {
          page: parseInt((query.page || 1).toString()),
          pageSize: parseInt((query.pageSize || 10).toString()),
          pageCount: 0,
          total: 0,
        },
      },
    };

    res.status(200).json(transformedData);
  } catch (error: any) {
    console.error("Admin payouts API error:", error);
    sendApiError(res, error, "Error fetching admin payouts");
  }
}

async function handleCreatePayout(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    const { partnerId, amount, method, payout_status = "pending" } = req.body;

    // Validate required fields
    if (!partnerId || !amount || !method) {
      return res.status(400).json({
        error: "Missing required fields: partnerId, amount, method",
      });
    }

    // Validate method enum
    if (!["paypal", "bank transfer"].includes(method)) {
      return res.status(400).json({
        error: "Invalid method. Must be 'paypal' or 'bank_transfer'",
      });
    }

    // Validate payout_status enum
    if (!["pending", "approved", "completed"].includes(payout_status)) {
      return res.status(400).json({
        error: "Invalid payout_status. Must be 'pending', 'approved', or 'completed'",
      });
    }

    // Validate amount
    if (typeof amount !== "number" || amount <= 0) {
      return res.status(400).json({
        error: "Amount must be a positive number",
      });
    }

    // Call StrapiAdminClient to create payout
    const data = await StrapiAdminClient.createPayout(
      {
        partnerId,
        amount,
        method,
        payout_status,
      },
      token!
    );

    res.status(201).json(data);
  } catch (error: any) {
    console.error("Create payout API error:", error);
    sendApiError(res, error, "Error creating payout");
  }
}
