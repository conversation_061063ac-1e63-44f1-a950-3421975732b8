import type { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST method
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Use StrapiClient to end the chat session without sessionId
    const response = await StrapiClient.endChatSession(token!);

    // Return the response from the backend
    return res.status(200).json(response);
  } catch (apiError: any) {
    console.error("API Client error ending session:", apiError);
    return res.status(apiError.status || 500).json({
      error: apiError.message || "Failed to end chat session",
    });
  }
}
