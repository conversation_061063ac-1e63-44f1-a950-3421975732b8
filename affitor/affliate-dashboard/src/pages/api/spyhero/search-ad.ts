import { StrapiClient } from "@/utils/request";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { keyword, platform } = req.body;

    if (!keyword || !platform) {
      return res.status(400).json({
        success: false,
        message: "Keyword and platform are required",
      });
    }

    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    const token = authHeader?.replace("Bearer ", "");

    // Call Strapi backend directly
    // const response = await fetch(
    //   `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/spyhero-ad/crawl-ads-with-keyword`,
    //   {
    //     method: "POST",
    //     headers: {
    //       "Content-Type": "application/json",
    //       ...(token && { Authorization: `Bearer ${token}` }),
    //     },
    //     body: JSON.stringify({ keyword, platform }),
    //   }
    // );

    const response = await StrapiClient.searchSpyHeroAds(
      keyword,
      platform,
      token || undefined
    );

    console.log("SpyHero search ad response:", response);

    // if (!response.ok) {
    //   return res.status(response.status).json({
    //     success: false,
    //     message: response.data.message || "Failed to search ads",
    //   });
    // }

    return res.status(200).json(response);
  } catch (error: any) {
    console.error("SpyHero search ad API error:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal server error",
    });
  }
}
