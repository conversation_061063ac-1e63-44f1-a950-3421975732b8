import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    switch (method) {
      case "GET":
        return handleGet(req, res, token!);
      case "POST":
        return handlePost(req, res, token!);
      default:
        return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error: any) {
    console.error("Pages API error:", error);
    return sendApiError(res, error, "An error occurred while processing your request");
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse, token: string) {
  try {
    const { page = 1, pageSize = 10, ...filters } = req.query;

    // Call Strapi API to get user's pages
    const response: any = await StrapiClient.client.get("/api/pages/my-pages", {
      params: {
        page,
        pageSize,
        filters,
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    // Debug logging
    console.log('Pages API - Strapi response structure:', {
      hasData: !!response.data,
      dataType: typeof response.data,
      isArray: Array.isArray(response.data),
      hasDataProperty: !!response.data?.data,
      keys: Object.keys(response.data || {}),
    });

    // Return the response data directly (it should already have the correct structure)
    return res.status(200).json(response.data);
  } catch (error: any) {
    console.error("Error fetching pages:", error);
    return sendApiError(res, error, "Failed to fetch pages");
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse, token: string) {
  try {
    const { data } = req.body;

    if (!data || !data.title) {
      return res.status(400).json({ 
        statusCode: 400, 
        message: "Page title is required" 
      });
    }

    // Call Strapi API to create page
    const response = await StrapiClient.client.post("/api/pages", {
      data,
    }, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    console.log('📄 [API] Page created, response:', response.data);
    return res.status(201).json(response.data);
  } catch (error: any) {
    console.error("Error creating page:", error);
    return sendApiError(res, error, "Failed to create page");
  }
}
