import { Html, <PERSON>, <PERSON>, NextScript } from "next/document";
import Script from "next/script";

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
        <meta
          name="impact-site-verification"
          content="097cd4d6-b237-462d-88d7-ec7dd6ffec8b"
        />
        <meta
          name="google-site-verification"
          content="GpLkNdqSDQEhBbYfAIl3LORMCrbXDDbvX5NWOCrjgP8"
        />
        {/* Google tag (gtag.js) */}
        {process.env.ENVIRONMENT === "production" && (
          <Script
            async
            src="https://www.googletagmanager.com/gtag/js?id=G-NSL02F3F87"
          ></Script>
        )}
        {/* Google Tag Manager */}
        {process.env.ENVIRONMENT === "production" && (
          <Script id="google-tag-manager-script" strategy="afterInteractive">
            {`
                            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                            })(window,document,'script','dataLayer','GTM-NZNSDN4W');
                        `}
          </Script>
        )}
      </Head>
      <body>
        {/* Google Tag Manager (noscript) */}
        {process.env.ENVIRONMENT === "production" && (
          <noscript>
            <iframe
              src="https://www.googletagmanager.com/ns.html?id=GTM-NZNSDN4W"
              height="0"
              width="0"
              style={{ display: "none", visibility: "hidden" }}
            ></iframe>
          </noscript>
        )}
        <Main />
        <NextScript />
        {process.env.ENVIRONMENT === "production" && (
          <Script id="microsoft-clarity-analytics">
            {`
                        (function(c,l,a,r,i,t,y){
                            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                        })(window, document, "clarity", "script", "qu38zp0eev");

                        // Function to track button clicks
                        document.addEventListener('click', function(event) {
                            if (event.target.tagName === 'BUTTON') {
                                clarity('track', 'button_click', {
                                    buttonText: event.target.innerText,
                                    buttonId: event.target.id || 'no-id'
                                });
                            }
                        });
                    `}
          </Script>
        )}
        {process.env.ENVIRONMENT === "production" && (
          <Script id="google-analytics">
            {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', 'G-NSL02F3F87');
                        `}
          </Script>
        )}
      </body>
    </Html>
  );
}
