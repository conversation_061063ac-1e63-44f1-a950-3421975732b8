import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actions, selectImageUploadLoading, selectImageUploadError } from '@/features/page/page.slice';
import { validateImageFile, getImageInfo } from '@/utils/image-upload';

const TestImageUpload: React.FC = () => {
  const dispatch = useDispatch();
  const uploadLoading = useSelector(selectImageUploadLoading);
  const uploadError = useSelector(selectImageUploadError);
  
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadResult, setUploadResult] = useState<string | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setSelectedFile(file);
    setUploadResult(null);

    // Validate the file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      alert(`Validation failed: ${validation.error}`);
      return;
    }

    const imageInfo = getImageInfo(file);
    console.log('Selected image info:', imageInfo);
  };

  const handleUpload = () => {
    if (!selectedFile) {
      alert('Please select a file first');
      return;
    }

    // Use a test page ID
    const testPageId = 'test-page-123';
    
    console.log('🧪 [Test Upload] Starting upload:', {
      fileName: selectedFile.name,
      fileSize: selectedFile.size,
      pageId: testPageId,
    });

    // Dispatch the upload action
    dispatch(actions.uploadImageRequest({ 
      file: selectedFile, 
      pageId: testPageId 
    }));
  };

  // Listen for upload success
  React.useEffect(() => {
    const handleUploadSuccess = (event: CustomEvent) => {
      const { s3Url } = event.detail;
      console.log('🧪 [Test Upload] Upload successful:', s3Url);
      setUploadResult(s3Url);
    };

    window.addEventListener('imageUploadSuccess', handleUploadSuccess as EventListener);
    
    return () => {
      window.removeEventListener('imageUploadSuccess', handleUploadSuccess as EventListener);
    };
  }, []);

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h1>Image Upload Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Step 1: Select an Image</h3>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          style={{ marginBottom: '10px' }}
        />
        
        {selectedFile && (
          <div style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
            <p><strong>Selected File:</strong> {selectedFile.name}</p>
            <p><strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
            <p><strong>Type:</strong> {selectedFile.type}</p>
          </div>
        )}
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Step 2: Upload to S3</h3>
        <button
          onClick={handleUpload}
          disabled={!selectedFile || uploadLoading}
          style={{
            padding: '10px 20px',
            backgroundColor: uploadLoading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: uploadLoading ? 'not-allowed' : 'pointer',
          }}
        >
          {uploadLoading ? 'Uploading...' : 'Upload Image'}
        </button>
      </div>

      {uploadError && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#f8d7da', 
          color: '#721c24', 
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <strong>Upload Error:</strong> {uploadError}
        </div>
      )}

      {uploadResult && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#d4edda', 
          color: '#155724', 
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <h3>Upload Successful!</h3>
          <p><strong>S3 URL:</strong></p>
          <p style={{ wordBreak: 'break-all', fontSize: '12px' }}>{uploadResult}</p>
          
          <h4>Preview:</h4>
          <img 
            src={uploadResult} 
            alt="Uploaded image" 
            style={{ maxWidth: '100%', maxHeight: '300px', border: '1px solid #ddd' }}
          />
        </div>
      )}

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#e9ecef', borderRadius: '4px' }}>
        <h3>Debug Info</h3>
        <p><strong>Upload Loading:</strong> {uploadLoading ? 'Yes' : 'No'}</p>
        <p><strong>Upload Error:</strong> {uploadError || 'None'}</p>
        <p><strong>Selected File:</strong> {selectedFile ? selectedFile.name : 'None'}</p>
        <p><strong>Upload Result:</strong> {uploadResult ? 'Success' : 'None'}</p>
      </div>

      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <h4>Instructions:</h4>
        <ol>
          <li>Select an image file (JPG, PNG, GIF, or WebP, max 5MB)</li>
          <li>Click "Upload Image" to test the Redux saga flow</li>
          <li>Check the browser console for detailed logs</li>
          <li>Verify the image appears in the preview if upload succeeds</li>
        </ol>
        
        <p><strong>Note:</strong> This test uses a dummy page ID. In the real editor, the actual page ID would be used.</p>
      </div>
    </div>
  );
};

export default TestImageUpload;
