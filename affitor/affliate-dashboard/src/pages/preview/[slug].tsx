import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import Head from 'next/head';
import { Button } from '@/components/ui/button';
import { actions } from '@/features/page/page.slice';
import {
  selectCurrentPage,
  selectPageLoading,
  selectPageError,
} from '@/features/page/page.slice';
import {
  ArrowLeft,
  Edit,
  Loader,
  AlertCircle,
} from 'lucide-react';

const PagePreview: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { slug } = router.query;

  // Redux state
  const currentPage = useSelector(selectCurrentPage);
  const loading = useSelector(selectPageLoading);
  const error = useSelector(selectPageError);

  // Load page data by slug
  useEffect(() => {
    if (slug && typeof slug === 'string') {
      // For now, we'll use the regular fetch since we don't have a by-slug endpoint yet
      // In a real implementation, you'd want to create a separate action for fetching by slug
      dispatch(actions.fetchPageRequest(slug));
    }
  }, [slug, dispatch]);

  // Render content with basic markdown-like formatting
  const renderContent = (content: any) => {
    if (!content) return null;
    
    let htmlContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
    
    // Basic markdown-like rendering
    htmlContent = htmlContent
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/^# (.*$)/gm, '<h1 class="text-3xl font-bold mb-4">$1</h1>')
      .replace(/^## (.*$)/gm, '<h2 class="text-2xl font-bold mb-3">$1</h2>')
      .replace(/^### (.*$)/gm, '<h3 class="text-xl font-bold mb-2">$1</h3>')
      .replace(/^- (.*$)/gm, '<li class="ml-4">• $1</li>')
      .replace(/^> (.*$)/gm, '<blockquote class="border-l-4 border-gray-300 pl-4 italic">$1</blockquote>')
      .replace(/\n/g, '<br>');

    return (
      <div 
        className="prose prose-lg max-w-none"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader className="h-5 w-5 animate-spin" />
          <span>Loading page...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold mb-2">Error Loading Page</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => router.push('/profile')}>
            Go Back to Profile
          </Button>
        </div>
      </div>
    );
  }

  if (!currentPage) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl font-semibold mb-2">Page Not Found</h1>
          <p className="text-gray-600 mb-4">The page you're looking for doesn't exist or is not published.</p>
          <Button onClick={() => router.push('/profile')}>
            Go Back to Profile
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>{currentPage.meta_title || currentPage.title || 'Page Preview'}</title>
        <meta 
          name="description" 
          content={currentPage.meta_description || currentPage.excerpt || 'Page preview'} 
        />
      </Head>

      <div className="min-h-screen bg-white dark:bg-gray-900">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>

              <div className="flex items-center gap-2">
                <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                  Preview Mode
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/editor/${currentPage.documentId}`)}
                >
                  <Edit className="h-4 w-4" />
                  Edit
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <article className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            {/* Featured Image */}
            {currentPage.featured_image && (
              <div className="aspect-video bg-gray-200 dark:bg-gray-700">
                <img
                  src={currentPage.featured_image.url}
                  alt={currentPage.featured_image.alternativeText || currentPage.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}

            {/* Content */}
            <div className="p-8">
              {/* Title */}
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {currentPage.title}
              </h1>

              {/* Excerpt */}
              {currentPage.excerpt && (
                <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                  {currentPage.excerpt}
                </p>
              )}

              {/* Meta Information */}
              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-8 pb-8 border-b border-gray-200 dark:border-gray-700">
                <span>
                  Published on {new Date(currentPage.publishedAt || currentPage.createdAt).toLocaleDateString()}
                </span>
                <span>•</span>
                <span>
                  By {currentPage.author?.username || 'Anonymous'}
                </span>
                {currentPage.view_count > 0 && (
                  <>
                    <span>•</span>
                    <span>
                      {currentPage.view_count} {currentPage.view_count === 1 ? 'view' : 'views'}
                    </span>
                  </>
                )}
              </div>

              {/* Page Content */}
              <div className="text-gray-900 dark:text-gray-100 leading-relaxed">
                {renderContent(currentPage.content)}
              </div>

              {/* Tags */}
              {currentPage.tags && currentPage.tags.length > 0 && (
                <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 flex-wrap">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Tags:
                    </span>
                    {currentPage.tags.map((tag: string, index: number) => (
                      <span
                        key={index}
                        className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </article>

          {/* Footer */}
          <div className="mt-8 text-center">
            <Button
              variant="outline"
              onClick={() => router.push(`/editor/${currentPage.documentId}`)}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Edit This Page
            </Button>
          </div>
        </main>
      </div>
    </>
  );
};

export default PagePreview;
