import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import SignInContainer from "@/containers/Authentication/SignIn";
import { useDispatch, useSelector } from "react-redux";
import { actions as discourseActions } from "@/features/discourse/discourse.slice";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";

const AuthenticationPage: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const [discourseParams, setDiscourseParams] = useState<{sso?: string, sig?: string} | null>(null);

  // Check for Discourse SSO parameters
  useEffect(() => {
    const { sso, sig, discourse } = router.query;

    // If we have SSO parameters from Discourse, store them
    if (sso && sig && discourse === 'true') {
      console.log('Detected Discourse SSO parameters');
      setDiscourseParams({
        sso: sso as string,
        sig: sig as string
      });
    }
  }, [router.query]);

  // <PERSON>le redirect back to Discourse after successful authentication
  useEffect(() => {
    // If user is authenticated and we have Discourse SSO parameters
    if (isAuthenticated && discourseParams?.sso && discourseParams?.sig) {
      console.log('User authenticated with Discourse SSO parameters, redirecting...');

      // Dispatch action to process SSO parameters from Discourse (reverse flow)
      dispatch(discourseActions.getDiscourseSSOUrl({
        sso: discourseParams.sso,
        sig: discourseParams.sig
      }));
    }
  }, [isAuthenticated, discourseParams, dispatch]);

  return <SignInContainer />;
};

export default AuthenticationPage;
