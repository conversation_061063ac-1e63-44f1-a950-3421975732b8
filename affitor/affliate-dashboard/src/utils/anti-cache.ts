/**
 * Utility functions for generating random strings for anti-caching purposes
 * Works in both browser and Node.js environments
 *
 * @example
 * ```typescript
 * import { generateAntiCacheUUID, addAntiCacheToUrl } from '@/utils/anti-cache';
 *
 * // Generate a random UUID for anti-caching
 * const cacheId = generateAntiCacheUUID();
 *
 * // Add anti-cache parameter to URL
 * const url = addAntiCacheToUrl('/api/data', 'cache_bust');
 * // Result: /api/data?cache_bust=abc123def456
 *
 * // Use in fetch requests
 * const response = await fetch(`/api/affiliates/${id}/url?anti_cache=${cacheId}`);
 * ```
 */

/**
 * Generates a random string for anti-caching purposes
 * @param length - Length of the random string (default: 16)
 * @returns Random string containing alphanumeric characters
 */
export function generateAntiCacheString(length: number = 16): string {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef<PERSON>ijklmnopqrstuvwxyz0123456789";
  let result = "";

  // Use crypto.getRandomValues if available (browser environment)
  if (
    typeof window !== "undefined" &&
    window.crypto &&
    window.crypto.getRandomValues
  ) {
    const array = new Uint8Array(length);
    window.crypto.getRandomValues(array);
    for (let i = 0; i < length; i++) {
      result += chars[array[i] % chars.length];
    }
    return result;
  }

  // Use Node.js crypto if available (server environment)
  if (typeof require !== "undefined") {
    try {
      const crypto = require("crypto");
      const bytes = crypto.randomBytes(length);
      for (let i = 0; i < length; i++) {
        result += chars[bytes[i] % chars.length];
      }
      return result;
    } catch (error) {
      // Fall back to Math.random if crypto is not available
    }
  }

  // Fallback to Math.random (less secure but works everywhere)
  for (let i = 0; i < length; i++) {
    result += chars[Math.floor(Math.random() * chars.length)];
  }

  return result;
}

/**
 * Generates a UUID-like string for anti-caching
 * @returns UUID-like string in format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
 */
export function generateAntiCacheUUID(): string {
  // Use crypto.randomUUID if available (modern browsers and Node.js 14.17+)
  if (
    typeof window !== "undefined" &&
    window.crypto &&
    window.crypto.randomUUID
  ) {
    return window.crypto.randomUUID();
  }

  // Use Node.js crypto.randomUUID if available
  if (typeof require !== "undefined") {
    try {
      const crypto = require("crypto");
      if (crypto.randomUUID) {
        return crypto.randomUUID();
      }
    } catch (error) {
      // Fall back to manual generation
    }
  }

  // Manual UUID generation (RFC 4122 version 4)
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Generates a timestamp-based anti-cache string
 * @returns Timestamp with random suffix
 */
export function generateTimestampAntiCache(): string {
  const timestamp = Date.now();
  const randomSuffix = generateAntiCacheString(8);
  return `${timestamp}_${randomSuffix}`;
}

/**
 * Adds anti-cache parameter to a URL
 * @param url - The base URL
 * @param paramName - Name of the anti-cache parameter (default: 'anti_cache')
 * @param useUUID - Whether to use UUID format (default: false)
 * @returns URL with anti-cache parameter added
 */
export function addAntiCacheToUrl(
  url: string,
  paramName: string = "anti_cache",
  useUUID: boolean = false
): string {
  const antiCacheValue = useUUID
    ? generateAntiCacheUUID()
    : generateAntiCacheString();
  const separator = url.includes("?") ? "&" : "?";
  return `${url}${separator}${paramName}=${antiCacheValue}`;
}
