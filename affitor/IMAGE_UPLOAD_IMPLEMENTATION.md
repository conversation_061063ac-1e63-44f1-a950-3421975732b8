# Image Upload Implementation for YooptaEditor (Redux Saga Pattern)

This document describes the complete implementation of image upload functionality for the page editor using S3 storage, following the Redux saga pattern used throughout the application.

## Overview

The image upload system allows users to add images to their pages using the YooptaRichTextEditor. When users insert images via the `/image` command or drag-and-drop, the images are automatically uploaded to S3 and the S3 URLs are stored in the page content. The implementation follows the existing Redux saga architecture for consistency with the rest of the application.

## Architecture

```
Frontend (YooptaEditor) → Redux Action → Saga → Next.js API Proxy → Strapi Backend → S3 Storage
                       ↓
                   Redux State Management
```

## Implementation Details

### 1. Backend (Strapi CMS)

#### API Endpoint: `/api/pages/upload-image`

**File**: `affitor/affiliate-cms/src/api/page/routes/custom-routes.ts`
- Added new route for image upload

**File**: `affitor/affiliate-cms/src/api/page/controllers/page.ts`
- Added `uploadImage` method with:
  - Authentication validation
  - File type validation (JPG, PNG, GIF, WebP)
  - File size validation (5MB limit)
  - Page ownership verification
  - S3 upload with organized folder structure

**S3 Folder Structure**:
```
/user-pages/{userId}/{pageId}/{timestamp}-{filename}
```

**Response Format**:
```json
{
  "s3Url": "https://bucket.s3.region.amazonaws.com/...",
  "fileName": "timestamp-filename.ext",
  "fileKey": "strapi/user-pages/userId/pageId/timestamp-filename.ext"
}
```

### 2. Frontend (Next.js Dashboard)

#### Redux State Management

**File**: `affitor/affliate-dashboard/src/features/page/page.slice.ts`
- Added `imageUploadLoading` and `imageUploadError` state
- Added Redux actions:
  - `uploadImageRequest` - Initiates image upload
  - `uploadImageSuccess` - Handles successful upload
  - `uploadImageFailure` - Handles upload errors
- Added selectors for image upload state

#### Redux Saga

**File**: `affitor/affliate-dashboard/src/features/page/page.saga.ts`
- Added `handleUploadImage` saga function
- Manages authentication token retrieval from localStorage
- Handles client-side validation before API call
- Makes API request to Next.js proxy endpoint
- Manages loading states, success, and error responses
- Follows same pattern as other sagas (`fetchPageSaga`, `autoSaveSaga`)

#### API Proxy: `/api/pages/upload-image`

**File**: `affitor/affliate-dashboard/src/pages/api/pages/upload-image.ts`
- Handles multipart/form-data uploads
- Validates file types and sizes
- Forwards requests to Strapi backend
- Returns S3 URLs to frontend

#### Image Upload Utility (Refactored)

**File**: `affitor/affliate-dashboard/src/utils/image-upload.ts`
- **Removed**: Direct API calls and token handling
- **Focus**: Client-side validation only
- `validateImageFile()` - Comprehensive file validation
- `getImageInfo()` - Extract file information
- `isValidImageFile()` - Simple boolean validation
- Helper functions for file processing

#### YooptaRichTextEditor Integration

**File**: `affitor/affliate-dashboard/src/components/YooptaRichTextEditor.tsx`
- Uses Redux hooks (`useDispatch`, `useSelector`)
- Dispatches `uploadImageRequest` action instead of direct API calls
- Monitors upload state from Redux store
- Enhanced Image plugin with Redux integration
- Handles loading states and errors from Redux state

**File**: `affitor/affliate-dashboard/src/pages/editor/[id].tsx`
- Passes `pageId` prop to YooptaRichTextEditor

## Usage

### For Users

1. **Insert Image via Command**:
   - Type `/image` in the editor
   - Select image from file picker
   - Image uploads automatically to S3
   - S3 URL is stored in page content

2. **Drag and Drop**:
   - Drag image files into the editor
   - Automatic upload and insertion

### For Developers

#### Using Redux Actions (Recommended)

```typescript
// In a React component
import { useDispatch, useSelector } from 'react-redux';
import { actions, selectImageUploadLoading, selectImageUploadError } from '@/features/page/page.slice';

const dispatch = useDispatch();
const uploadLoading = useSelector(selectImageUploadLoading);
const uploadError = useSelector(selectImageUploadError);

// Dispatch upload action
const handleImageUpload = (file: File, pageId: string) => {
  dispatch(actions.uploadImageRequest({ file, pageId }));
};

// Monitor upload state
useEffect(() => {
  if (uploadError) {
    console.error('Upload failed:', uploadError);
  }
}, [uploadError]);
```

#### Using Validation Utility

```typescript
// Client-side validation before upload
import { validateImageFile, getImageInfo } from '@/utils/image-upload';

const validation = validateImageFile(file);
if (!validation.isValid) {
  console.error('Validation failed:', validation.error);
  return;
}

const imageInfo = getImageInfo(file);
console.log('Image info:', imageInfo);
```

## Configuration

### Environment Variables

**Strapi Backend** (`.env`):
```
AWS_ACCESS_KEY_ID=your_access_key
AWS_ACCESS_SECRET=your_secret_key
AWS_REGION=us-east-1
AWS_BUCKET=your-bucket-name
ROOT_PATH_UPLOAD=strapi
```

**Next.js Frontend** (`.env.local`):
```
NEXT_PUBLIC_STRAPI_URL=http://localhost:1337
```

### Dependencies

**Frontend**:
```json
{
  "formidable": "^3.5.1",
  "form-data": "^4.0.0",
  "@types/formidable": "^3.4.5"
}
```

**Backend**:
```json
{
  "@aws-sdk/client-s3": "^3.x.x"
}
```

## Security Features

1. **Authentication**: Only authenticated users can upload images
2. **Authorization**: Users can only upload to pages they own
3. **File Validation**: 
   - Type validation (images only)
   - Size limits (5MB max)
   - Extension validation
4. **S3 Security**: Public read access with organized folder structure

## Error Handling

### Common Errors

1. **File too large**: Returns 413 status with clear message
2. **Invalid file type**: Returns 400 status with supported types
3. **Unauthorized**: Returns 403 status for permission issues
4. **Page not found**: Returns 404 status if page doesn't exist

### Frontend Error Display

- Progress indicators during upload
- Error messages for failed uploads
- Retry mechanisms for network issues
- Graceful fallbacks for upload failures

## Testing

### Manual Testing

1. Create a new page in the editor
2. Try uploading different image types (JPG, PNG, GIF, WebP)
3. Test file size limits (try files > 5MB)
4. Test drag and drop functionality
5. Verify images persist after page save/reload

### API Testing

```bash
# Test upload endpoint
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "image=@test-image.jpg" \
  -F "pageId=your-page-id" \
  http://localhost:3000/api/pages/upload-image
```

## Performance Considerations

1. **Immediate Upload**: Images upload immediately when added (not on page save)
2. **Progress Tracking**: Real-time upload progress feedback
3. **Concurrent Uploads**: Multiple images can be uploaded simultaneously
4. **Memory Management**: Temporary files are cleaned up after upload
5. **CDN**: S3 URLs can be served through CloudFront for better performance

## Future Enhancements

1. **Image Optimization**: Automatic resizing and compression
2. **Multiple Formats**: Generate different sizes (thumbnail, medium, large)
3. **Alt Text**: Automatic alt text generation using AI
4. **Bulk Upload**: Drag and drop multiple images at once
5. **Image Gallery**: Browse and reuse previously uploaded images
6. **Compression**: Client-side image compression before upload

## Troubleshooting

### Common Issues

1. **Upload fails silently**: Check browser console for errors
2. **S3 permissions**: Verify AWS credentials and bucket permissions
3. **CORS issues**: Ensure proper CORS configuration in S3
4. **File size**: Check both frontend and backend size limits
5. **Authentication**: Verify JWT token is valid and not expired

### Debug Logging

Enable detailed logging by checking browser console for:
- `[YooptaEditor]` - Editor-related logs
- `[Upload Image API]` - API proxy logs
- `[Page Saga]` - Redux state logs

## Monitoring

### Metrics to Track

1. Upload success/failure rates
2. Average upload times
3. File size distributions
4. Error types and frequencies
5. S3 storage usage

### Alerts

Set up alerts for:
- High upload failure rates
- S3 storage quota approaching limits
- Unusual file upload patterns
- Authentication failures
