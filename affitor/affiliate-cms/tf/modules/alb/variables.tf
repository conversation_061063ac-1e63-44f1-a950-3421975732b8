variable "certificate_arn" {
  description = "The ARN of the ACM certificate to use for HTTPS."
  type        = string
  default     = null
}

variable "ecs_service_name" {
  description = "The name of the ECS service to associate with the ALB."
  type        = string
  default     = null
}

variable "vpc_id" {
  description = "The VPC ID where resources will be deployed."
  type        = string
}

variable "public_subnets" {
  description = "List of public subnet IDs."
  type        = list(string)
}

variable "environment" {
  description = "Environment name"
  type        = string
}
