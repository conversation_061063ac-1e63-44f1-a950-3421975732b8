/**
 * Yoopta Editor TypeScript Definitions
 * 
 * This file contains TypeScript interfaces for Yoopta Editor content structure
 * to ensure type safety across the backend and frontend codebase.
 */

export interface YooptaBlock {
  id: string;
  type: YooptaBlockType;
  value: YooptaBlockValue[];
  meta?: YooptaBlockMeta;
}

export interface YooptaBlockMeta {
  order: number;
  depth?: number;
  align?: 'left' | 'center' | 'right';
  [key: string]: any;
}

export interface YooptaBlockValue {
  text?: string;
  children?: YooptaBlockValue[];
  [key: string]: any;
}

export type YooptaBlockType = 
  | 'paragraph'
  | 'heading-one'
  | 'heading-two'
  | 'heading-three'
  | 'heading-four'
  | 'heading-five'
  | 'heading-six'
  | 'blockquote'
  | 'bulleted-list'
  | 'numbered-list'
  | 'code'
  | 'divider'
  | 'image'
  | 'video'
  | 'link'
  | 'file'
  | 'callout'
  | 'table'
  | 'accordion'
  | 'embed';

export interface YooptaContentValue {
  [blockId: string]: YooptaBlock;
}

/**
 * Page content structure with Yoopta Editor integration
 */
export interface PageContent {
  /** Rich text content in Yoopta Editor format */
  content?: YooptaContentValue;
  /** HTML representation for SEO and fallback rendering */
  content_html?: string;
  /** Plain text representation for search and excerpts */
  content_plain?: string;
}

/**
 * Utility types for content processing
 */
export interface ContentProcessingResult {
  html: string;
  plainText: string;
  wordCount: number;
  readingTime: number; // in minutes
}

/**
 * Content validation result
 */
export interface ContentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Helper functions type definitions
 */
export interface YooptaContentHelpers {
  convertToHtml: (content: YooptaContentValue) => string;
  convertToPlainText: (content: YooptaContentValue) => string;
  validateContent: (content: YooptaContentValue) => ContentValidationResult;
  processContent: (content: YooptaContentValue) => ContentProcessingResult;
  extractTextFromValue: (value: YooptaBlockValue[]) => string;
  generateUniqueBlockId: () => string;
  createEmptyBlock: (type: YooptaBlockType, order?: number) => YooptaBlock;
}

/**
 * Export/Import formats
 */
export interface YooptaExportOptions {
  format: 'html' | 'markdown' | 'plaintext' | 'json';
  includeMetadata?: boolean;
  preserveFormatting?: boolean;
}

export interface YooptaImportOptions {
  format: 'html' | 'markdown' | 'plaintext';
  preserveStructure?: boolean;
  generateIds?: boolean;
}

/**
 * Editor configuration types
 */
export interface YooptaEditorConfig {
  autoSave?: boolean;
  autoSaveInterval?: number;
  placeholder?: string;
  readOnly?: boolean;
  allowedBlocks?: YooptaBlockType[];
  maxBlocks?: number;
  enabledFeatures?: {
    toolbar?: boolean;
    shortcuts?: boolean;
    dragAndDrop?: boolean;
    blockSelection?: boolean;
  };
}

/**
 * Content statistics
 */
export interface ContentStats {
  blockCount: number;
  wordCount: number;
  characterCount: number;
  readingTime: number;
  blockTypes: Record<YooptaBlockType, number>;
}

/**
 * Extend the global Strapi types to include our page content type
 */
declare module '@strapi/strapi' {
  export interface Strapi {
    contentTypes: {
      'api::page.page': {
        attributes: {
          title: string;
          slug: string;
          content?: YooptaContentValue;
          content_html?: string;
          content_plain?: string;
          excerpt?: string;
          featured_image?: any;
          meta_title?: string;
          meta_description?: string;
          status: 'draft' | 'published' | 'archived';
          view_count: number;
          author: any;
          tags?: string[];
          last_edited_at?: string;
          createdAt: string;
          updatedAt: string;
          publishedAt?: string;
        };
      };
    };
  }
}
