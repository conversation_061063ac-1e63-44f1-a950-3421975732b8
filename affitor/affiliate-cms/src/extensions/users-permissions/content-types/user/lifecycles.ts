export default {
  async afterFindMany(event) {
    const { result } = event;
    if (!result || !result.length) return;

    // Get commission service
    const commissionService = strapi.service('api::referral-commission.referral-commission') as any;

    for (let i = 0; i < result.length; i++) {
      const user = result[i];

      // Find the referral for this user (assuming user.id is linked to referral.user)
      const referral = await strapi.entityService.findMany('api::referral.referral', {
        filters: { user: { id: user.id } },
        fields: ['id'],
      });

      let totalEarnings = 0;
      let totalRevenue = 0;

      if (referral && referral.length > 0) {
        const statsArray = await Promise.all(
          referral.map((ref) => commissionService.getCommissionStats(ref.id, false))
        );
        totalEarnings = statsArray.reduce((sum, stats) => sum + (stats.totalEarnings || 0), 0);
        totalRevenue = statsArray.reduce((sum, stats) => sum + (stats.totalRevenue || 0), 0);
      }
	  	
      result[i] = {
        ...user,
        totalEarnings,	
        totalRevenue,
      };
    }
  },
};
