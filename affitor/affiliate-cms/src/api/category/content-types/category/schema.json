{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category", "description": "Organize your content into categories"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "slug": {"type": "uid"}, "description": {"type": "text"}, "affiliates": {"type": "relation", "relation": "manyToMany", "target": "api::affiliate.affiliate", "mappedBy": "categories"}}}