/**
 * user-tracking-request service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService(
  'api::user-tracking-request.user-tracking-request',
  ({ strapi }) => ({
    // Get or create user tracking record
    async getUserTracking(userId) {
      // Find existing tracking record
      const existingRecord = await strapi.entityService.findMany(
        'api::user-tracking-request.user-tracking-request',
        {
          filters: {
            users_permissions_user: userId,
          },
          populate: ['subscription_tier'],
        }
      );

      console.log('Existing tracking record:', !!existingRecord);

      // Return existing or create new
      if (existingRecord && existingRecord.length > 0) {
        return existingRecord[0];
      }

      const globalSettings = await strapi.entityService.findMany('api::global.global');

      // Create new tracking record with default values using document API
      return await strapi.documents('api::user-tracking-request.user-tracking-request').create({
        data: {
          users_permissions_user: userId,
          request_count: 0,
          request_limit: globalSettings[0]?.default_free_requests || 200, // Default limit
          last_request_date: new Date(),
          statistics: {}, // Initialize empty statistics object
          subscription_tier: null,
        },
        status: 'published', // Set the status directly to published
      });
    },

    // Track a user request and check if they're within limits
    async trackUserRequest(userId, path = '/') {
      const tracking = await this.getUserTracking(userId);

      // Check if user is at or over limit
      if (tracking.request_count >= tracking.request_limit) {
        return {
          allowed: false,
          remaining: 0,
          limit: tracking.request_limit,
          count: tracking.request_count,
        };
      }

      // Prepare statistics object to track path counts
      const statistics = tracking.statistics || {};
      if (!statistics[path]) {
        statistics[path] = 0;
      }
      statistics[path]++;

      // Increment request count
      const updated = await strapi.entityService.update(
        'api::user-tracking-request.user-tracking-request',
        tracking.id,
        {
          data: {
            request_count: tracking.request_count + 1,
            last_request_date: new Date(),
            statistics: statistics,
          },
        }
      );

      const remaining = updated.request_limit - updated.request_count;

      return {
        allowed: true,
        remaining,
        limit: updated.request_limit,
        count: updated.request_count,
        pathStats: statistics[path],
      };
    },

    // Get user stats
    async getUserStats(userId) {
      const tracking = await this.getUserTracking(userId);

      return {
        request_count: tracking.request_count,
        request_limit: tracking.request_limit,
        remaining: tracking.request_limit - tracking.request_count,
        last_request_date: tracking.last_request_date,
        statistics: tracking.statistics || {},
      };
    },

    // Reset user counter (admin function)
    async resetUserCounter(userId, resetStatistics = false) {
      const tracking = await this.getUserTracking(userId);

      const updateData: any = {
        request_count: 0,
        last_request_date: new Date(),
      };

      // Optionally reset statistics as well
      if (resetStatistics) {
        updateData.statistics = {};
      }

      return await strapi.entityService.update(
        'api::user-tracking-request.user-tracking-request',
        tracking.id,
        {
          data: updateData,
        }
      );
    },

    // Update user subscription tier
    async updateSubscription(userId, tier, limit = 0, currentPeriodEnd = null, transaction = null) {
      console.log('Updating subscription for user:', userId, 'Tier:', tier.name, 'Limit:', limit);
      const tracking = await this.getUserTracking(userId);

      // Get the subscription tier from the database if it exists
      let newLimit = limit;
      let tierData = null;
      let tierId = null;

      // If tier is a relation object with ID, or a direct ID
      if (typeof tier === 'object' && tier !== null && tier.id) {
        tierId = tier.id;
        tierData = tier;
      } else if (typeof tier === 'number') {
        tierId = tier;

        try {
          tierData = await strapi.entityService.findOne(
            'api::subscription-tier.subscription-tier',
            tierId
          );
        } catch (error) {
          console.error('Error fetching subscription tier by ID:', error);
        }
      } else if (typeof tier === 'string' && tier !== 'free') {
        // If tier is a string (name), find by name
        try {
          const tiers = await strapi.entityService.findMany(
            'api::subscription-tier.subscription-tier',
            {
              filters: {
                name: tier,
                publishedAt: { $ne: null },
              },
            }
          );
          if (tiers && tiers.length > 0) {
            tierData = tiers[0];
            tierId = tierData.id;
          }
        } catch (error) {
          console.error('Error fetching subscription tier by name:', error);
        }
      } else if (tier === 'free') {
        // Find the free tier (price = 0)
        try {
          const tiers = await strapi.entityService.findMany(
            'api::subscription-tier.subscription-tier',
            {
              filters: {
                price: 0,
                publishedAt: { $ne: null },
              },
            }
          );
          if (tiers && tiers.length > 0) {
            tierData = tiers[0];
            tierId = tierData.id;
          }
        } catch (error) {
          console.error('Error fetching free tier:', error);
        }
      }

      // Use tier request_limit if available, otherwise use provided limit or default
      if (tierData && tierData.request_limit && !newLimit) {
        newLimit = tierData.request_limit;
      }

      // Calculate end date
      let endDate = null;

      if (tierData && tierData.price > 0) {
        endDate = new Date();

        if (tierData.duration_days) {
          endDate.setDate(endDate.getDate() + tierData.duration_days);
        } else {
          endDate.setDate(endDate.getDate() + 30); // 30 days as fallback
        }
      }

      console.log('tierId:', tierId);

      const updateData: any = {
        // Keep for backward compatibility
        subscription_tier: tierId,
      };

      if (newLimit) {
        updateData.request_limit = newLimit;
      }

      if (currentPeriodEnd) {
        updateData.current_period_end = currentPeriodEnd;
      }

      if (transaction) {
        updateData.transaction = transaction;
      }

      // Update tracking record
      await strapi.db.query('api::user-tracking-request.user-tracking-request').update({
        where: { id: tracking.id },
        data: updateData,
      });

      // Update user record with subscription tier
      if (userId && tierId !== null) {
        try {
          await strapi.entityService.update('plugin::users-permissions.user', userId, {
            data: {
              subscription_tier: tierId,
            },
          });
          console.log(`User ${userId} subscription tier updated to ${tierId}`);
        } catch (error) {
          console.error('Error updating user subscription tier:', error);
        }
      }

      return;
    },
  })
);
