/**
 * top-ad controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::top-ad.top-ad', ({ strapi }) => ({
  /**
   * Search for trending ads by keyword
   */
  async searchTrendingAds(ctx) {
    try {
      const { keyword, page, limit, period } = ctx.query;
      const params = {
        page: page || 1,
        limit: limit || 20,
        period: period || 7,
        country: ctx.query.country || 'US',
        order_by: ctx.query.order_by || 'ctr',
      };

      // Call with legacy mode (keyword as first parameter)
      const result = await strapi.service('api::top-ad.top-ad').searchTrendingAds(keyword, params);

      ctx.body = result;
    } catch (err) {
      console.error('Error in searchTrendingAds controller:', err);
      ctx.body = {
        success: false,
        error: err.message || 'An error occurred',
      };
    }
  },

  /**
   * Get transcript for an ad
   */
  async getAdTranscript(ctx) {
    try {
      const { user } = ctx.state;
      const { adId } = ctx.params;

      if (!adId) {
        return ctx.badRequest('Ad ID is required');
      }

      const result = await strapi.service('api::top-ad.top-ad').getAdTranscript(adId, user.id);

      ctx.body = result;
    } catch (err) {
      console.error('Error in getTranscript controller:', err);
      ctx.body = {
        success: false,
        error: err.message || 'An error occurred while getting transcript',
      };
    }
  },

  /**
   * Override the default find method to implement custom filtering and fetch ads if none exist
   */
  async find(ctx) {
    try {
      // Add is_displayed filter
      console.log('ctx.query:', ctx.query);
      const { filters } = ctx.query;
      const baseFilters = {
        ...(typeof filters === 'object' && filters !== null ? filters : {}),
        is_displayed: true,
      };

      // Store original query params
      const originalQuery = { ...ctx.query };
      const pagination = ctx.query.pagination as { pageSize?: string } | undefined;
      const requestedLimit = parseInt(pagination?.pageSize || '25', 10);

      // Handle sorting logic - sort can be an array or string
      let sortField = null;
      if (ctx.query.sort) {
        const sortValue = Array.isArray(ctx.query.sort) ? ctx.query.sort[0] : ctx.query.sort;
        sortField = typeof sortValue === 'string' ? sortValue.split(':')[0] : '';
      }

      console.log(`Requested sort field: ${sortField}`);
      console.log(`Requested limit: ${requestedLimit}`);

      // If no sort field specified, use the normal filters
      ctx.query.filters = baseFilters;

      // First, check if we have any top ads
      const existingAdsCount = await strapi.db.query('api::top-ad.top-ad').count();

      // If no ads exist, fetch them based on affiliate URLs
      if (existingAdsCount === 0) {
        console.log('No top ads found, fetching based on affiliate URLs...');

        // Get all affiliates with traffic_webs relation
        const affiliates = await strapi.documents('api::affiliate.affiliate').findMany({
          status: 'published',
          populate: ['airtable_data'],
        });

        console.log(`Found ${affiliates.length} affiliates`);

        if (affiliates && affiliates.length > 0) {
          console.log(`Found ${affiliates.length} affiliates to process for ads`);

          // Process each affiliate
          for (const affiliate of affiliates) {
            console.log(`Processing ads for affiliate ${affiliate.name}`);

            // Simply pass the affiliate object to the service
            const searchResult = await strapi
              .service('api::top-ad.top-ad')
              .searchTrendingAds(affiliate, { limit: 20 });

            console.log(`Completed search for affiliate: ${affiliate.name}`);
          }

          console.log('Completed fetching ads for all affiliates');
        }
      }

      // First, fetch records where the sort field is not null
      if (sortField) {
        // Create filter for non-null and positive values
        ctx.query.filters = {
          ...baseFilters,
          [sortField]: {
            $notNull: true,
            $gt: 0,
          },
        };

        // Execute first query
        const firstResult = await super.find(ctx);
        const firstResultCount = firstResult.data.length;

        console.log(`First query returned ${firstResultCount} records`);

        // If we have enough records, return the result
        if (firstResultCount >= requestedLimit) {
          return firstResult;
        }

        // Otherwise, fetch additional records with null sort field values
        ctx.query = { ...originalQuery };
        ctx.query.filters = {
          ...baseFilters,
          $or: [{ [sortField]: { $null: true } }, { [sortField]: { $lte: 0 } }],
        };

        // Add pagination to only fetch what we still need
        ctx.query.pagination = {
          page: 1,
          pageSize: (requestedLimit - firstResultCount).toString(),
        };

        // Execute second query
        const secondResult = await super.find(ctx);

        console.log(`Second query returned ${secondResult.data.length} records`);

        // Combine results
        return {
          data: [...firstResult.data, ...secondResult.data],
          meta: {
            ...secondResult.meta,
            pagination: {
              ...secondResult.meta.pagination,
              total: firstResult.meta.pagination.total + secondResult.meta.pagination.total,
            },
          },
        };
      }

      // Call the default find method with our modified query
      return await super.find(ctx);
    } catch (err) {
      console.error('Error in find controller:', err);
      return ctx.badRequest(err);
    }
  },
}));
