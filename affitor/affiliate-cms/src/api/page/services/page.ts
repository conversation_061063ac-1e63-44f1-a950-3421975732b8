/**
 * page service
 */

import { factories } from '@strapi/strapi';
import type { YooptaContentValue, YooptaBlock, YooptaBlockValue } from '../../../../types/yoopta-editor';

// Helper function to convert Yoopta content to HTML
function convertYooptaToHtml(content: YooptaContentValue | null | undefined): string {
  if (!content || typeof content !== 'object') {
    return '';
  }

  let html = '';

  // Sort blocks by order if meta.order exists
  const blocks = Object.values(content).sort((a: YooptaBlock, b: YooptaBlock) => {
    const orderA = a.meta?.order || 0;
    const orderB = b.meta?.order || 0;
    return orderA - orderB;
  });

  for (const block of blocks) {
    const typedBlock = block as YooptaBlock;

    switch (typedBlock.type) {
      case 'paragraph':
        html += `<p>${extractTextFromValue(typedBlock.value)}</p>`;
        break;
      case 'heading-one':
        html += `<h1>${extractTextFromValue(typedBlock.value)}</h1>`;
        break;
      case 'heading-two':
        html += `<h2>${extractTextFromValue(typedBlock.value)}</h2>`;
        break;
      case 'heading-three':
        html += `<h3>${extractTextFromValue(typedBlock.value)}</h3>`;
        break;
      case 'blockquote':
        html += `<blockquote>${extractTextFromValue(typedBlock.value)}</blockquote>`;
        break;
      case 'bulleted-list':
        html += `<ul><li>${extractTextFromValue(typedBlock.value)}</li></ul>`;
        break;
      case 'numbered-list':
        html += `<ol><li>${extractTextFromValue(typedBlock.value)}</li></ol>`;
        break;
      case 'code':
        html += `<pre><code>${extractTextFromValue(typedBlock.value)}</code></pre>`;
        break;
      default:
        html += `<div>${extractTextFromValue(typedBlock.value)}</div>`;
    }
  }

  return html;
}

// Helper function to convert Yoopta content to plain text
function convertYooptaToPlainText(content: YooptaContentValue | null | undefined): string {
  if (!content || typeof content !== 'object') {
    return '';
  }

  let text = '';

  // Sort blocks by order if meta.order exists
  const blocks = Object.values(content).sort((a: YooptaBlock, b: YooptaBlock) => {
    const orderA = a.meta?.order || 0;
    const orderB = b.meta?.order || 0;
    return orderA - orderB;
  });

  for (const block of blocks) {
    const typedBlock = block as YooptaBlock;
    const blockText = extractTextFromValue(typedBlock.value);
    if (blockText.trim()) {
      text += blockText + '\n\n';
    }
  }

  return text.trim();
}

// Helper function to extract text from Yoopta value array
function extractTextFromValue(value: YooptaBlockValue[]): string {
  if (!Array.isArray(value)) {
    return '';
  }

  return value.map(item => {
    if (typeof item === 'string') {
      return item;
    }
    if (item && typeof item === 'object' && item.text) {
      return item.text;
    }
    if (item && typeof item === 'object' && item.children) {
      return extractTextFromValue(item.children);
    }
    return '';
  }).join('');
}

export default factories.createCoreService('api::page.page', ({ strapi }) => ({
  /**
   * Create a new page for a user
   */
  async createUserPage(userId: number, pageData: any) {
    try {
      // Process content to generate HTML and plain text versions
      const processedData = {
        ...pageData,
        author: userId,
        last_edited_at: new Date().toISOString(),
        status: 'draft',
      };

      // Generate HTML and plain text if content is provided
      if (pageData.content) {
        processedData.content_html = convertYooptaToHtml(pageData.content);
        processedData.content_plain = convertYooptaToPlainText(pageData.content);
      }

      const page = await strapi.documents('api::page.page').create({
        data: processedData,
        status: 'draft',
      });

      return page;
    } catch (error) {
      console.error('Error creating user page:', error);
      throw error;
    }
  },

  /**
   * Update a page with auto-save functionality
   */
  async autoSavePage(pageId: string, userId: number, content: any) {
    try {
      // Verify ownership
      const existingPage = await strapi.documents('api::page.page').findOne({
        documentId: pageId,
        populate: ['author'],
      });

      if (!existingPage || existingPage.author.id !== userId) {
        throw new Error('Page not found or access denied');
      }

      // Process content and generate HTML/plain text versions
      const updateData: any = {
        content,
        last_edited_at: new Date().toISOString(),
      };

      // Generate HTML and plain text versions
      if (content) {
        updateData.content_html = convertYooptaToHtml(content);
        updateData.content_plain = convertYooptaToPlainText(content);
      }

      // Update with auto-save data
      const updatedPage = await strapi.documents('api::page.page').update({
        documentId: pageId,
        data: updateData,
      });

      return updatedPage;
    } catch (error) {
      console.error('Error auto-saving page:', error);
      throw error;
    }
  },

  /**
   * Publish a page
   */
  async publishPage(pageId: string, userId: number) {
    try {
      // Verify ownership
      const existingPage = await strapi.documents('api::page.page').findOne({
        documentId: pageId,
        populate: ['author'],
      });

      if (!existingPage || existingPage.author.id !== userId) {
        throw new Error('Page not found or access denied');
      }

      // Publish the page
      const publishedPage = await strapi.documents('api::page.page').update({
        documentId: pageId,
        data: { status: 'published' },
      });

      return publishedPage;
    } catch (error) {
      console.error('Error publishing page:', error);
      throw error;
    }
  },

  /**
   * Get user's pages with filtering and pagination
   */
  async getUserPages(userId: number, filters: any = {}, pagination: any = {}) {
    try {
      const pages = await strapi.documents('api::page.page').findMany({
        filters: {
          author: userId,
          ...filters
        },
        populate: ['featured_image'],
        sort: { createdAt: 'desc' },
        ...pagination,
      });

      return { pages, pagination: { total: pages.length } };
    } catch (error) {
      console.error('Error fetching user pages:', error);
      throw error;
    }
  },

  /**
   * Get a page by slug (for public access)
   */
  async getPageBySlug(slug: string) {
    try {
      const pages = await strapi.documents('api::page.page').findMany({
        filters: {
          slug,
          status: 'published'
        },
        populate: ['author', 'featured_image'],
        limit: 1,
      });

      return pages[0] || null;
    } catch (error) {
      console.error('Error fetching page by slug:', error);
      throw error;
    }
  },

  /**
   * Generate a unique slug for a page
   */
  async generateUniqueSlug(title: string, excludeId?: string) {
    try {
      let baseSlug = title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');

      let slug = baseSlug;
      let counter = 1;

      const untilBreak = true;
      while (untilBreak) {
        const filters: any = { slug };
        if (excludeId) {
          filters.documentId = { $ne: excludeId };
        }

        const existingPages = await strapi.documents('api::page.page').findMany({
          filters,
          limit: 1,
        });

        if (existingPages.length === 0) {
          break;
        }

        slug = `${baseSlug}-${counter}`;
        counter++;
      }

      return slug;
    } catch (error) {
      console.error('Error generating unique slug:', error);
      throw error;
    }
  },

  /**
   * Duplicate a page
   */
  async duplicatePage(pageId: string, userId: number) {
    try {
      // Get the original page
      const originalPage = await strapi.documents('api::page.page').findOne({
        documentId: pageId,
        populate: ['author'],
      });

      if (!originalPage || originalPage.author.id !== userId) {
        throw new Error('Page not found or access denied');
      }

      // Generate unique title and slug
      const newTitle = `${originalPage.title} (Copy)`;
      const newSlug = await this.generateUniqueSlug(newTitle);

      // Create duplicate
      const duplicateData = {
        title: newTitle,
        slug: newSlug,
        content: originalPage.content,
        excerpt: originalPage.excerpt,
        meta_title: originalPage.meta_title,
        meta_description: originalPage.meta_description,
        tags: originalPage.tags,
        author: userId,
        status: 'draft' as const,
        last_edited_at: new Date().toISOString(),
      };

      const duplicatedPage = await strapi.documents('api::page.page').create({
        data: duplicateData,
        status: 'draft',
      });

      return duplicatedPage;
    } catch (error) {
      console.error('Error duplicating page:', error);
      throw error;
    }
  },
}));
