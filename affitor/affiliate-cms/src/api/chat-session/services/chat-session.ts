/**
 * chat-session service
 */

import { factories } from '@strapi/strapi';
import { v4 as uuidv4 } from 'uuid';
import { IChatSession } from '../interfaces';

export default factories.createCoreService('api::chat-session.chat-session', {
  async createSession(userId: string): Promise<IChatSession> {
    try {
      const sessionId = `session-${uuidv4()}`;
      const session = await strapi.entityService.create('api::chat-session.chat-session', {
        data: {
          session_id: sessionId,
          start_time: new Date(),
          session_status: 'active',
          users_permissions_user: { id: userId },
        },
      });
      return { ...session, id: String(session.id) } as IChatSession;
    } catch (error) {
      console.error('Error creating chat session:', error);
      throw error;
    }
  },

  async getActiveSession(userId: string): Promise<IChatSession | any | null> {
    try {
      const sessions = await strapi.entityService.findMany('api::chat-session.chat-session', {
        filters: {
          session_status: 'active',
          users_permissions_user: {
            id: userId,
          },
        },
        sort: { start_time: 'desc' },
        populate: ['users_permissions_user'],
        limit: 1,
      });

      return sessions && sessions.length > 0
        ? { ...sessions[0], id: String(sessions[0].id) }
        : null;
    } catch (error) {
      console.error('Error getting active session:', error);
      throw error;
    }
  },

  async terminateSession(sessionId: string): Promise<IChatSession> {
    try {
      // First find the session by its session_id field
      const sessions = await strapi.entityService.findMany('api::chat-session.chat-session', {
        filters: {
          session_id: sessionId,
        },
        limit: 1,
      });

      if (!sessions || sessions.length === 0) {
        throw new Error(`Session with session_id ${sessionId} not found`);
      }

      const session = await strapi.entityService.update(
        'api::chat-session.chat-session',
        sessions[0].id,
        {
          data: {
            end_time: new Date(),
            session_status: 'ended',
          },
        }
      );
      return { ...session, id: String(session.id) } as IChatSession;
    } catch (error) {
      console.error('Error terminating session:', error);
      throw error;
    }
  },

  async terminateAllActiveSessions(userId: string): Promise<any> {
    try {
      console.log('LOG-terminateAllActiveSessions', userId);
      // Get all active sessions for the user
      const activeSessions = await strapi.entityService.findMany('api::chat-session.chat-session', {
        filters: {
          session_status: 'active',
          users_permissions_user: {
            // Fixed: changed from users_permission_user to users_permissions_user
            id: userId,
          },
        },
      });

      console.log('LOG-activeSessions', activeSessions);
      if (!activeSessions || activeSessions.length === 0) {
        return { count: 0, sessions: [] };
      }

      // Instead of using updateMany with complex relations,
      // get the session IDs and update them individually or use a simpler where clause
      const sessionIds = activeSessions.map((session) => session.id);

      // Update all sessions using a direct ID list instead of relations
      await strapi.db.query('api::chat-session.chat-session').updateMany({
        where: {
          id: { $in: sessionIds },
        },
        data: {
          end_time: new Date(),
          session_status: 'ended',
        },
      });

      // Return the updated session IDs and count
      return {
        count: activeSessions.length,
        sessions: activeSessions.map((session) => ({
          ...session,
          id: String(session.id),
          session_status: 'ended',
          end_time: new Date(),
        })),
      };
    } catch (error) {
      console.error('Error terminating all active sessions:', error);
      throw error;
    }
  },
});
