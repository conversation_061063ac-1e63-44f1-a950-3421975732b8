'use strict';

import puppeteer from 'puppeteer';

interface TikTokVideoData {
  video_id: string;
  title: string;
  description: string;
  channel_id: string;
  channel_title: string;
  channel_avatar: string;
  published_from: string;
  thumbnail: string;
  duration: string;
  video_link: string;
  views: number;
  likes: number;
  comments: number;
  shares: number;
  type: string;
  platform: string;
  is_displayed: boolean;
  is_verified: boolean;
  is_from_crawler: boolean;
}

class TikTokCrawler {
  private browser: any = null;
  private page: any = null;

  async init() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
        ],
      });
    }

    if (!this.page) {
      this.page = await this.browser.newPage();

      // Set user agent to avoid detection
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );

      // Set viewport
      await this.page.setViewport({ width: 1366, height: 768 });
    }
  }

  async crawlTikTokSearch(keyword: string): Promise<TikTokVideoData[]> {
    try {
      await this.init();

      // Navigate to TikTok search page
      const searchUrl = `https://www.tiktok.com/search?q=${encodeURIComponent(keyword)}&t=${Date.now()}`;
      console.log(`Navigating to TikTok search: ${searchUrl}`);

      await this.page.goto(searchUrl, {
        waitUntil: 'networkidle2',
        timeout: 30000,
      });

      // Wait for video items to load
      await this.page.waitForSelector('[data-e2e="search_top-item"]', { timeout: 15000 });

      // Scroll down to load more content
      await this.autoScroll();

      // Extract video data using a string-based function to avoid TypeScript DOM issues
      const videos = await this.page.evaluate(`
        (() => {
          const videoElements = document.querySelectorAll('[data-e2e="search_top-item"]');
          const results = [];

          videoElements.forEach((element) => {
            try {
              // Extract video link and ID
              const linkElement = element.querySelector('a[href*="/video/"]');
              if (!linkElement) return;

              const videoLink = linkElement.href;
              const videoIdMatch = videoLink.match(/\\/video\\/(\\d+)/);
              if (!videoIdMatch) return;

              const videoId = videoIdMatch[1];

              // Extract title and description from video caption
              const captionElement = element.querySelector('[data-e2e="search-card-video-caption"]');
              const titleText = captionElement?.textContent?.trim() || '';
              
              // Extract views
              const viewsElement = element.querySelector('[data-e2e="video-views"]');
              const viewsText = viewsElement?.textContent?.trim() || '0';
              
              // Parse views function
              const parseViews = (viewsText) => {
                if (!viewsText) return 0;
                
                const cleanText = viewsText.replace(/[^\\d.KMB]/gi, '');
                
                if (cleanText.includes('K')) {
                  return Math.floor(parseFloat(cleanText.replace('K', '')) * 1000);
                } else if (cleanText.includes('M')) {
                  return Math.floor(parseFloat(cleanText.replace('M', '')) * 1000000);
                } else if (cleanText.includes('B')) {
                  return Math.floor(parseFloat(cleanText.replace('B', '')) * 1000000000);
                }
                
                return parseInt(cleanText) || 0;
              };
              
              // Parse time function
              const parseTimeText = (timeText) => {
                if (!timeText) return new Date().toISOString();
                
                if (timeText.includes('-')) {
                  const days = parseInt(timeText.split('-')[1]) || 1;
                  const date = new Date();
                  date.setDate(date.getDate() - days);
                  return date.toISOString();
                }
                
                const now = new Date();
                if (timeText.includes('h')) {
                  const hours = parseInt(timeText) || 1;
                  now.setHours(now.getHours() - hours);
                } else if (timeText.includes('d')) {
                  const days = parseInt(timeText) || 1;
                  now.setDate(now.getDate() - days);
                } else if (timeText.includes('w')) {
                  const weeks = parseInt(timeText) || 1;
                  now.setDate(now.getDate() - (weeks * 7));
                }
                
                return now.toISOString();
              };

              const views = parseViews(viewsText);

              // Extract thumbnail
              const thumbnailElement = element.querySelector('img');
              const thumbnail = thumbnailElement?.src || '';

              // Extract channel info
              const channelElement = element.querySelector('[data-e2e="search-card-user-unique-id"]');
              const channelTitle = channelElement?.textContent?.trim() || '';
              
              // Extract channel avatar
              const avatarElement = element.querySelector('.css-1zpj2q-ImgAvatar');
              const channelAvatar = avatarElement?.src || '';

              // Extract time posted
              const timeElement = element.querySelector('.css-df7371-DivTimeTag');
              const timeText = timeElement?.textContent?.trim() || '';
              const publishedFrom = parseTimeText(timeText);

              results.push({
                video_id: videoId,
                title: titleText.slice(0, 200), // Limit title length
                description: titleText,
                channel_id: channelTitle, // Using channel title as ID for now
                channel_title: channelTitle,
                channel_avatar: channelAvatar,
                published_from: publishedFrom,
                thumbnail: thumbnail,
                duration: '0:00', // TikTok doesn't show duration in search results
                video_link: videoLink,
                views: views,
                likes: 0, // Not available in search results
                comments: 0, // Not available in search results
                shares: 0, // Not available in search results
                type: 'video',
                platform: 'tiktok',
                is_displayed: false,
                is_verified: false,
                is_from_crawler: true, // Mark as from crawler
              });
            } catch (error) {
              console.error('Error extracting video data:', error);
            }
          });

          return results;
        })()
      `);

      console.log(`Extracted ${videos.length} videos from TikTok search`);
      return videos;
    } catch (error) {
      console.error('Error crawling TikTok search:', error);
      throw error;
    }
  }

  private async autoScroll() {
    try {
      await this.page.evaluate(`
        (async () => {
          await new Promise((resolve) => {
            let totalHeight = 0;
            const distance = 100;
            const timer = setInterval(() => {
              const scrollHeight = document.body.scrollHeight;
              window.scrollBy(0, distance);
              totalHeight += distance;

              if (totalHeight >= scrollHeight || totalHeight >= 3000) {
                clearInterval(timer);
                resolve();
              }
            }, 100);
          });
        })()
      `);

      // Wait for new content to load
      await new Promise((resolve) => setTimeout(resolve, 2000));
    } catch (error) {
      console.error('Error during auto scroll:', error);
    }
  }

  async close() {
    if (this.page) {
      await this.page.close();
      this.page = null;
    }

    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}

export default TikTokCrawler;
