/**
 * aiscript-session controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController(
  'api::aiscript-session.aiscript-session',
  ({ strapi }) => ({
    async create(ctx) {
      try {
        const { user } = ctx.state;

        if (!user) {
          return ctx.unauthorized('You must be logged in to create an AI script session');
        }

        const sessionId = await strapi
          .service('api::aiscript-session.aiscript-session')
          .createSession({ sessionId: 'session-id' + new Date().toISOString(), userId: user.id });

        return {
          data: { sessionId },
        };
      } catch (err) {
        console.error('Error in create AI script session controller:', err);
        return ctx.badRequest('Failed to create AI script session', { error: err });
      }
    },

    async endSession(ctx) {
      try {
        const { user } = ctx.state;
        const { sessionId } = ctx.request.body;

        if (sessionId) {
          // Check if the sessionId is valid
          const session = await strapi
            .service('api::aiscript-session.aiscript-session')
            .getActiveSessionById(sessionId);

          if (!session) {
            return ctx.notFound('Session not found');
          }

          // End the session
          await strapi.service('api::aiscript-session.aiscript-session').endSession(sessionId);

          return {
            message: 'AI script session ended successfully',
          };
        }
        if (!user) {
          return ctx.unauthorized('You must be logged in to end AI script sessions');
        }

        const result = await strapi
          .service('api::aiscript-session.aiscript-session')
          .terminateAllActiveSessions(user.id);

        return {
          data: result.sessions,
          meta: {
            count: result.count,
            message:
              result.count > 0
                ? 'AI script sessions terminated'
                : 'No active AI script sessions found',
          },
        };
      } catch (err) {
        console.error('Error in end AI script session controller:', err);
        return ctx.badRequest('Failed to end AI script sessions', { error: err });
      }
    },
  })
);
